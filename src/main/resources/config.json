{"schedule": {"time_regex": "15 0/6 * * * ?"}, "partners": [{"state": "inactive", "name": "GCOOP_ECOM_OUT", "type": "sftp", "traffic": "out", "host": "**************", "port": 22, "user_name": "ONEPAY", "password": "123456A@", "private_key": [], "folder_from": "/cdr/gcoop/inbox/", "folder_to": "/2020/", "folder_backup": "/cdr/gcoop/backup/inbox/", "file_format": "DEV_PAY_BANK_ONEPAY_VCB_(\\d{8}).csv", "backup": "yes"}, {"state": "active", "name": "HOMECREDIT_ECOM_OUT", "type": "sftp", "traffic": "out", "host": "**************", "port": 2222, "user_name": "onepay_usr", "password": "", "private_key": ["-----BEGIN RSA PRIVATE KEY-----", "MIIEowIBAAKCAQEA0e36VB5Nzeh2mULzM1akQ+DxwFBNYqAHediYK0J4u+zcWbyQ", "ddOJT2zSgAtFUdq0bTRe5mWQ5JHT0TU1yeTsg9eL2pS+WTxPzwfiZxUc2uPXL9ft", "21hmMbXizOdR/tMaC4DM8KLBRtXLP1eaSciy47FBifT4l1rdV3MEGxBnKH9rkhWY", "hNzBFryY6ear1G9iOSq6jjcyARRkWUAzr+nleUoan2jpy8LMaNYuYEep/91wOHrv", "pY01kKVzJeTjPAGEJ2fPHhG/sRf9MrdJo9NpISMzH3vXm77PI2IKjNaFab1KdGz7", "DH4VW0pJnep4a4VShQtBa8ImcP6IES7B2zSx1wIDAQABAoIBAD86eSo60l5RRN9W", "bd+pmeIj70xoI1awMnA9j1+ALtMTPS79/1ittO6qLSCJxIAp6iv2GRs1vhVJ4s1h", "RGkhvSbcle/o2wgPhU4q5sL7dexrsVVkqOxmzKjuSnhCt8yTkoWibpePvbzzCbtz", "TDIq885mhgHiObmEkaQULUllgFyOr/6epqqsALff0MqNjROZdKpAqfPieLetHi6R", "Jf1Nu5mh/pofAQA/2nnbq/bmhN74fsRmkv/v+JmrzcDTQnzVXXBw45NCaNiDrIhj", "pJ0xdRpysXTRlhsh/XQP8rC7HF490nukUZOpR0IColoJrs7z9y7rRvLz8wX+qwDD", "sMZ05ckCgYEA/juk5WVxw6edKAlk4ZyM9WPVv2L2ZaIomLeElncbZ4ZNfEVjwN/w", "GeZHBKhTYJ5rNNAZ+xY7r0R52yC1VJ0dyLwRBtinSr7Sbn35ZzbRKenLM14fWotE", "Jpfnc6evxFEOcxlh5Osc0PKM6zmKpAfrUYqqUeY0EoZsPZW2myGItDMCgYEA02OB", "PqeZbYQS/qo9PUDqyXADDT2wnIMox1fRHCVlLtT+gk+FKi0IyFqcreF5uu7bsDxv", "OrDeyqnASzH7PC/ZSc+qM0dL7QREK0vA3gtZcsUqRU2BpXrt8oiiJhsRL2Uy9mk1", "Uw9oDRrBzKyCQ9aq7UrO1ri0/7KtkLfYXPX1B80CgYEAuwSBLsbLQ1R/xT3z2gth", "nfpM+LcB7o21M3J8tHr1y9f+FBavZOjXJRZC0+hHPWIMFxOmqRAX47yJr2qGObJC", "gLHoDJM/rTpmbiV5XDX089IqSqVIh0C/Z2yDNI78O3wtFvprCwzxa7uhckAn/rZc", "ouVQmWKwl5gO89a4N7IZcAUCgYB3E0U5uwVFWLGufM00VP4s7Ij7i3WSJJvy9rfX", "aAVARUD8vLihkm6xFcHOfQWqVGSyOMjbQciuVsL4WLZSKA35cx5H9nCBmfaggPKq", "4a/IY+I02b0fL0kcZvUYDAU5MoNrpMYh/Lq7LwheOArFFgaA1rP+5lsm8kqtC7K4", "YuBdPQKBgB/B0wdXmRv5Gi8Kc6/hNaW9JeViqHlDdCmHL2E5BGwIcVjUx7BMwVmL", "LaDdBgwkYaJoclib4FWCYmlESQGEmEXbse8yHp6bs8ufigJciQFpANNz5CMrHvp6", "T/d3YflMApYnlMtWDcPNW3Ys+W9OR8IzkdzuSegrRS80D78XeofA", "-----END RSA PRIVATE KEY-----"], "folder_from": "/cdr/homecredit/inbox/", "folder_to": "/ftp/collection/", "folder_backup": "/cdr/homecredit/backup/inbox/", "file_format": "ONEPAY_HC_(\\d{8}).txt", "backup": "yes"}, {"state": "inactive", "name": "VNPT_EPAY_OUT", "type": "sftp", "traffic": "out", "host": "***********", "port": 2022, "user_name": "megapayvn", "password": "Mega^pay3222*", "private_key": [], "folder_from": "/cdr/vnptepay/inbox/", "folder_to": "/home/<USER>/", "folder_backup": "/cdr/vnptepay/backup/inbox/", "file_format": "(\\d{8})_VNPTEPAY_ALL.csv|(\\d{8})_VNPTEPAY1_ALL.csv", "backup": "yes"}, {"state": "active", "name": "NAPAS_AGRIBANK_IN", "type": "ftp", "traffic": "in", "host": "**********", "port": 21, "user_name": "onepay_pg", "password": "One1pay_Bn", "private_key": "", "folder_from": "/Inbox", "folder_to": "/cdr/banknet/agribank/onecomm/inbox/", "folder_check_exist": "/cdr/banknet/agribank/onecomm/backup/inbox/", "folder_backup": "/Backup/", "file_format": "(\\d{8})_TRANS_DETAIL_010074_ONEPAY_RF.txt|(\\d{8})_TRANS_DETAIL_010074_ONEPAY_DP.txt", "backup": "yes"}, {"state": "active", "name": "NAPAS_AGRIBANK_OUT", "type": "ftp", "traffic": "out", "host": "**********", "port": 21, "user_name": "onepay_pg", "password": "One1pay_Bn", "private_key": "", "folder_from": "/cdr/banknet/agribank/onecomm/outbox/", "folder_to": "/Outbox/", "folder_backup": "/cdr/banknet/agribank/onecomm/backup/outbox/", "file_format": "(\\d{8})_TRANS_DETAIL_970405_ONEPAY_PM.txt|(\\d{8})_TRANS_DETAIL_970405_ONEPAY_RQ.txt", "backup": "yes"}, {"state": "active", "name": "NAPAS_CALLOP_IN", "type": "ftp", "traffic": "in", "host": "**********", "port": 21, "user_name": "onepay_ncc", "password": "A1x<ztu4", "private_key": "", "folder_from": "/Inbox", "folder_to": "/cdr/banknet/callop/inbox/", "folder_check_exist": "/cdr/banknet/callop/backup/inbox/", "folder_backup": "/Backup/", "file_format": "O_(\\d{8})_500001_DETAIL.dat", "backup": "yes"}, {"state": "active", "name": "NAPAS_BILLING_IN", "type": "ftp", "traffic": "in", "host": "**********", "port": 21, "user_name": "onepay_bill", "password": "Bw1<Axtu", "private_key": "", "folder_from": "/Inbox", "folder_to": "/cdr/banknet/onebill/inbox/", "folder_check_exist": "/cdr/banknet/onebill/backup/inbox/", "folder_backup": "/Backup/", "file_format": "O_(\\d{8})_000006_DETAIL.dat", "backup": "yes"}, {"state": "stop", "name": "NAPAS_BANK_IN", "type": "ftp", "traffic": "in", "host": "**********", "port": 21, "user_name": "onepay_bank_pg", "password": "Uvc>3Bpg", "private_key": "", "folder_from": "/Inbox", "folder_to": "/cdr/banknet/opbn/onecomm/inbox/", "folder_check_exist": "/cdr/banknet/opbn/onecomm/backup/inbox/", "folder_backup": "/Backup/", "file_format": "O_(\\d{8})_010074_RQ.dat", "backup": "yes"}, {"state": "stop", "name": "NAPAS_BANK_OUT", "type": "ftp", "traffic": "out", "host": "**********", "port": 21, "user_name": "onepay_bank_pg", "password": "Uvc>3Bpg", "private_key": "", "folder_from": "/cdr/banknet/opbn/onecomm/outbox", "folder_to": "/Outbox/", "folder_backup": "/cdr/banknet/opbn/onecomm/backup/outbox/", "file_format": "I_(\\d{8})_010074_RF.dat", "backup": "yes"}, {"state": "active", "name": "VIETTEL_IN", "type": "ftp", "traffic": "in", "host": "***************", "port": 21, "user_name": "ftp_onepay", "password": "ysy!DB1501", "private_key": "", "folder_from": "/live/kpp/day", "folder_to": "/cdr/viettel/inbox/", "folder_check_exist": "/cdr/viettel/backup/inbox/", "folder_backup": "", "file_format": "(\\d{8})_VIETTEL_KPP_TPP_ONEPAY.cdr", "backup": "no"}, {"state": "active", "name": "LAZADA_ECOM_OUT", "type": "sftp", "traffic": "out", "host": "isftp.alipay.com", "port": 22, "user_name": "openvise_onepay", "password": "R5EI7G", "private_key": [], "folder_from": "/cdr/lazada/ecom/inbox/", "folder_to": "/upload/onepay/", "folder_backup": "/cdr/lazada/ecom/backup/inbox/", "file_format": "lzd_mpay_(\\d{12})", "backup": "yes"}, {"state": "inactive", "name": "VINMART_ALL_OUT", "type": "sftp", "traffic": "out", "host": "***************", "port": 22, "user_name": "P2r6QeG1UrLJ2SdK", "password": "ay7a@WraGB", "private_key": [], "folder_from": "/cdr/vinmart/inbox/", "folder_to": "/OnePay/", "folder_backup": "/cdr/vinmart/backup/inbox/", "file_format": "DEV_PAY_BANK_ONEPAY_VCB_(\\d{8}).xml", "backup": "yes"}, {"state": "active", "name": "ECOM_NAPAS_IN", "type": "sftp", "traffic": "in", "host": "***************", "port": 2443, "user_name": "onepay", "password": "v6@zSQ7z", "private_key": [], "folder_from": "/ECOM/Inbox/", "folder_to": "/cdr/napas/ecom/afterin/", "folder_check_exist": "/cdr/napas/ecom/backup/inbox/", "folder_backup": "/ECOM/Backup/", "file_format": "(\\d{6})_ACQ_ONEPAYWL(\\d{1})_971018_(\\d+)_TC_ECOM.dat|(\\d{6})_ACQ_ONEPAYWL(\\d{1})_971018_(\\d+)_XL_ECOM.dat", "backup": "yes"}, {"state": "active", "name": "ECOM_NAPAS_OUT", "type": "sftp", "traffic": "out", "host": "***************", "port": 2443, "user_name": "onepay", "password": "v6@zSQ7z", "private_key": [], "folder_from": "/cdr/napas/ecom/preout/", "folder_to": "/ECOM/Outbox/", "folder_backup": "/cdr/napas/ecom/backup/outbox/", "file_format": "(\\d{6})_ACQ_ONEPAYWL(\\d{1})_971018_(\\d+)_SL_ECOM.dat", "backup": "yes"}, {"state": "active", "name": "BILLING_NAPAS_IN", "type": "sftp", "traffic": "in", "host": "***************", "port": 2443, "user_name": "onepay", "password": "v6@zSQ7z", "private_key": [], "folder_from": "/VAS/Inbox/", "folder_to": "/cdr/napas/vas/inbox/", "folder_backup": "/VAS/Backup/", "file_format": "(\\d{6})_VAS_TPP_INC_ONP905008.dat|(\\d{6})_VAS_TPP_ACK_ONP905008.dat|(\\d{6})_VAS_TPP_REF_ONP905008.dat", "backup": "yes"}, {"state": "active", "name": "BILLING_NAPAS_OUT", "type": "sftp", "traffic": "out", "host": "***************", "port": 2443, "user_name": "onepay", "password": "v6@zSQ7z", "private_key": [], "folder_from": "/cdr/napas/vas/outbox/", "folder_to": "/VAS/Outbox/", "folder_backup": "/cdr/napas/vas/backup/outbox/", "file_format": "(\\d{6})_VAS_TPP_REF_ONP905008.dat", "backup": "yes"}, {"state": "active", "name": "ECOM_CUP_IN", "type": "sftp", "traffic": "in", "host": "*************", "port": 9990, "user_name": "onepay_p", "password": "", "private_key": ["-----BEGIN RSA PRIVATE KEY-----", "MIIEpQIBAAKCAQEA2m5MMHBc48VXV7l58Fqum4EN7nD8MZpZuAjDFzeIXZnqRLoY", "Jf8Fdt7YgphZzHo0MZRUNSDDUEqnvqNk67RnEp5imTaVHGg0Oo6vrvJScI3X2UQl", "HH1Fug+dyWuVGMSRPALpxANbHRipcanShNHyp+j+t8RCJRP0nZh+ZQG47r8rJijR", "fHu1cOmVzE7MtcFEgUi7ywz2e2Cd0cBg5aWyP/Lg3fsMrqzI9I6CMFcidp5E5M9j", "qk6dHtV7ExKnvckvNy1Ht/Z3WUX+qTNtbOlV+W1fv/Vy+67Fj8CzqKQyvTRiOlOr", "XcP3r6tw4Jryt7iZVh8Fz3MLccGPjalMrQvRPQIDAQABAoIBAB+gUrW8vyZXf2L7", "PvWXG6r2T58TiuBfudfUpPKqnrr2ux3CWBbASvlAINcf2fiqRP5cvKiImt74QDtx", "Cv65uybN+fa3GXXofI9x82wAOAGF+BfELx9l3/+lnTH5urfAVkr7HP+9gKbA2F/V", "t951S44WRTM0XmNh9UwQNVTIoA8pKDp6W8qT7ooAQ5mL+xb+FEeWLYfBEGomT8Yq", "vNVchk9PNFUwZKfAhVmHHgmaUmLlVDCwYhubQCCmUSKWNEZlyVCwxOZmQ4EVsIig", "YYoS7w5neDRqGYzcUwDfYb1oWXmHX8xbf77tSNBo3R7HkUcuMo8I18zXJPhs26jO", "BAHIcf0CgYEA9vZoTfUujgUQTvCDSYl+sAyve6Krio5rLFrhHTl/+0uW/uHgYo+A", "d4Gmr4GU+KKuT2FeLLW7wbzg3fssB/wFJgI4DopvMyRdBFH5lFNYO+n/icKBRXx7", "lBpnf+SukbTL2IoRA1R3hFl8kHVLHx6H4ZoCnb6GmipNN0tU1oKWKk8CgYEA4myZ", "k443IuQESp0bpg6UPJ9ENf0XQLojcZ0BjdxnAwL+ksq9npkVrixKOcaEVCbE8+vV", "f+fiUO5d/3XAE0o/trGEDyQAIhiogmKj5X9s3bMztSIcCiEFYx7mCn5u5qr8SnU5", "IVIWCqr8Te9agi8LK6fW1Ui3g/ylvYTXSTKtBLMCgYEAoXyCu+dFJ+y7u0KeNqnH", "w4qn/OpjzVhPUFOkvB1rSIEnHUfKF+jKElsfCchsByEpZhK/J/E18vt09vJU2mu7", "Wh09VpTDHef7QuXvk0PkgFkElaowKaW6kR+q6gA4/mIAoXm2ojzLYim5wCRSmOi3", "5Zd1enypO4nJsAx8T0BYvRUCgYEAzgCUcYVQcLQXm3L5NsnI4Kbevmf4AfHYUYPR", "aCoApuLQjulaP7Nz4DV3ir0spAtp4arNYkOsyok0iDmveV1yPluflM7iOCNLHOV1", "zFV0zPg/sSrJbAXDCv2Pujhx2ffy9iNUyRYP68DJMHb2V79JYHyhnwc7wB/bA2Sb", "IoOymrUCgYEA1Q6VMDUYuGBB4xrnp3/5xNYKFJ30qhWo7PoMPAVoJpazMXJqeKlY", "MPf8hKXt5t65zJi6MkC9VYy7yKIRbHpQu4bFdwYUR+WJglfxdrlbQ+oxr7bjEXv2", "UpVwsl6HwfpIOsjL+tC5rUdp1zmhIFFXK06sxnGd21N8P1cvKSuuy8w=", "-----END RSA PRIVATE KEY-----"], "folder_from": "/datap/onepay_p/0828390704/out/", "folder_to": "/cdr/cup/outbox/", "folder_backup": "/datap/onepay_p/0828390704/backup/", "file_format": "^IFD(\\d{8})(ACOM|AERR)$", "backup": "yes"}, {"state": "active", "name": "ECOTEK_ALL_OUT", "type": "sftp", "traffic": "out", "host": "***************", "port": 22, "user_name": "onepay", "password": "$2MEhmDb|ALXL8YM14OJ", "private_key": [], "folder_from": "/cdr/ecotek/outbox/", "folder_to": "/files/", "folder_backup": "/cdr/ecotek/backup/outbox/", "file_format": "(\\d{8})_ECOTEK_ALL.csv", "backup": "yes"}, {"state": "inactive", "name": "SOHAGAME_ALL_OUT", "type": "sftp", "traffic": "out", "host": "*************", "port": 52348, "user_name": "doisoat", "password": "2CzQMi0wrXPdr8kPDmyj", "private_key": [], "folder_from": "/cdr/sohagame/outbox/", "folder_to": "/data/", "folder_backup": "/cdr/sohagame/backup/outbox/", "file_format": "(\\d{8})_SOHAGAME_ALL.csv", "backup": "yes"}, {"state": "active", "name": "GUCCI_ALL_OUT", "type": "sftp", "traffic": "out", "host": "***************", "port": 22, "user_name": "blackline.onepay.to.kering", "password": "Hagzn94tBS", "private_key": [], "folder_from": "/cdr/gucci/outbox/", "folder_to": "/", "folder_backup": "/cdr/gucci/backup/outbox/", "file_format": "(\\d{8})_GUCCI_ALL.csv", "backup": "yes"}, {"state": "active", "name": "KBANK_BNPL_DP_OUT", "type": "pgp_encrypt", "public_key": "kbank_pgp_pub_key.asc", "folder_from": "/cdr/kbank/bnpl/temp/outbox/", "folder_to": "/cdr/kbank/bnpl/outbox/", "folder_backup": "/cdr/kbank/bnpl/temp/backup/outbox/", "file_format": "^(KBANK_BNPL_)[0-9]{8}(_DP.csv)$", "file_extend": ".pgp", "backup": "yes"}, {"state": "active", "name": "KBANK_BNPL_IN", "type": "pgp_decrypt", "private_key": "kbank_pgp_pri_key.asc", "passphrase": "4n8c8f5T", "file_extend": ".pgp", "folder_from": "/cdr/kbank/bnpl/inbox/", "folder_to": "/cdr/kbank/bnpl/temp/inbox/", "folder_backup": "/cdr/kbank/bnpl/backup/inbox/", "file_format": "^(\\d{8}) Transaction Settlement.csv.pgp$|^(\\d{8}) Summary Settlement.csv.pgp$", "backup": "yes"}, {"state": "active", "name": "KBANK_UPOS_REFUND_IN", "type": "pgp_decrypt", "private_key": "kbank_pgp_pri_key.asc", "passphrase": "4n8c8f5T", "file_extend": ".pgp", "folder_from": "/cdr/kbankupos/inbox/", "folder_to": "/cdr/kbankupos/temp/inbox/", "folder_backup": "/cdr/kbankupos/backup/inbox/", "file_format": "^(Onepay_Refund_)(0[1-9]|[12][0-9]|3[01])(0[1-9]|1[012])[0-9]{2}_[0-9]{2}(.xlsx.pgp)$", "backup": "yes"}, {"state": "active", "name": "KBANK_UPOS_REFUND_OUT_OR_DP", "type": "pgp_encrypt", "public_key": "kbankupos_pgp_pub_key.asc", "folder_from": "/cdr/kbankupos/temp/outbox/", "folder_to": "/cdr/kbankupos/outbox/", "folder_backup": "/cdr/kbankupos/backup/outbox/", "file_format": "^(Onepay_Refund_)(0[1-9]|[12][0-9]|3[01])(0[1-9]|1[012])[0-9]{2}_[0-9]{2}(.xlsx)$|^Settlement_Details_[0-9]{8}_[0-9]{3}(_dp.csv)$|^Settlement_Details_[0-9]{8}_[0-9]{3}(_dp.xlsx)$", "backup": "yes", "file_extend": ".pgp"}, {"state": "active", "name": "KBANK_MERCHANTS_QR", "type": "pgp_decrypt", "private_key": "kbank_pgp_pri_key.asc", "passphrase": "4n8c8f5T", "file_extend": ".pgp", "folder_from": "/cdr/kbankupos/QRMerchantList/inbox/", "folder_to": "/cdr/kbankupos/QRMerchantList/outbox/", "folder_backup": "/cdr/kbankupos/QRMerchantList/backup/", "file_format": "(.pgp)$", "backup": "yes"}, {"state": "active", "name": "AGRIBANK_DIRECT_ST_IN", "type": "aes_decrypt", "key": "N8yiEnqBc8TFK3OX", "init_vector": "****************", "file_extend": ".dec", "target_extend": ".txt", "folder_from": "/cdr/agribank/onecom/outbox/", "folder_to": "/cdr/agribank/onecom/outbox/", "folder_backup": "/cdr/agribank/onecom/backup/outbox/", "file_format": "^((\\d{8})|(\\d{8})_SETTLE).dec$", "backup": "yes"}, {"state": "active", "name": "EXP_BIGMERCHANT_SETTLEMENT", "type": "sftp", "traffic": "out", "host": "b2b-sftp.apple.com", "port": 22, "user_name": "onepay", "password": "", "private_key": ["-----BEGIN RSA PRIVATE KEY-----", "MIIEowIBAAKCAQEA0e36VB5Nzeh2mULzM1akQ+DxwFBNYqAHediYK0J4u+zcWbyQ", "ddOJT2zSgAtFUdq0bTRe5mWQ5JHT0TU1yeTsg9eL2pS+WTxPzwfiZxUc2uPXL9ft", "21hmMbXizOdR/tMaC4DM8KLBRtXLP1eaSciy47FBifT4l1rdV3MEGxBnKH9rkhWY", "hNzBFryY6ear1G9iOSq6jjcyARRkWUAzr+nleUoan2jpy8LMaNYuYEep/91wOHrv", "pY01kKVzJeTjPAGEJ2fPHhG/sRf9MrdJo9NpISMzH3vXm77PI2IKjNaFab1KdGz7", "DH4VW0pJnep4a4VShQtBa8ImcP6IES7B2zSx1wIDAQABAoIBAD86eSo60l5RRN9W", "bd+pmeIj70xoI1awMnA9j1+ALtMTPS79/1ittO6qLSCJxIAp6iv2GRs1vhVJ4s1h", "RGkhvSbcle/o2wgPhU4q5sL7dexrsVVkqOxmzKjuSnhCt8yTkoWibpePvbzzCbtz", "TDIq885mhgHiObmEkaQULUllgFyOr/6epqqsALff0MqNjROZdKpAqfPieLetHi6R", "Jf1Nu5mh/pofAQA/2nnbq/bmhN74fsRmkv/v+JmrzcDTQnzVXXBw45NCaNiDrIhj", "pJ0xdRpysXTRlhsh/XQP8rC7HF490nukUZOpR0IColoJrs7z9y7rRvLz8wX+qwDD", "sMZ05ckCgYEA/juk5WVxw6edKAlk4ZyM9WPVv2L2ZaIomLeElncbZ4ZNfEVjwN/w", "GeZHBKhTYJ5rNNAZ+xY7r0R52yC1VJ0dyLwRBtinSr7Sbn35ZzbRKenLM14fWotE", "Jpfnc6evxFEOcxlh5Osc0PKM6zmKpAfrUYqqUeY0EoZsPZW2myGItDMCgYEA02OB", "PqeZbYQS/qo9PUDqyXADDT2wnIMox1fRHCVlLtT+gk+FKi0IyFqcreF5uu7bsDxv", "OrDeyqnASzH7PC/ZSc+qM0dL7QREK0vA3gtZcsUqRU2BpXrt8oiiJhsRL2Uy9mk1", "Uw9oDRrBzKyCQ9aq7UrO1ri0/7KtkLfYXPX1B80CgYEAuwSBLsbLQ1R/xT3z2gth", "nfpM+LcB7o21M3J8tHr1y9f+FBavZOjXJRZC0+hHPWIMFxOmqRAX47yJr2qGObJC", "gLHoDJM/rTpmbiV5XDX089IqSqVIh0C/Z2yDNI78O3wtFvprCwzxa7uhckAn/rZc", "ouVQmWKwl5gO89a4N7IZcAUCgYB3E0U5uwVFWLGufM00VP4s7Ij7i3WSJJvy9rfX", "aAVARUD8vLihkm6xFcHOfQWqVGSyOMjbQciuVsL4WLZSKA35cx5H9nCBmfaggPKq", "4a/IY+I02b0fL0kcZvUYDAU5MoNrpMYh/Lq7LwheOArFFgaA1rP+5lsm8kqtC7K4", "YuBdPQKBgB/B0wdXmRv5Gi8Kc6/hNaW9JeViqHlDdCmHL2E5BGwIcVjUx7BMwVmL", "LaDdBgwkYaJoclib4FWCYmlESQGEmEXbse8yHp6bs8ufigJciQFpANNz5CMrHvp6", "T/d3YflMApYnlMtWDcPNW3Ys+W9OR8IzkdzuSegrRS80D78XeofA", "-----END RSA PRIVATE KEY-----"], "folder_from": "/cdr/bigmerchant/outbox/settlement/", "folder_to": "/out/settlement/", "folder_backup": "/cdr/bigmerchant/backup/settlement/", "file_format": "^O_(\\d{14})_(.+)_SETTLEMENT_DETAIL.csv.gpg$", "backup": "yes"}, {"state": "active", "name": "EXP_BIGMERCHANT_SETTLEMENT_GPG", "type": "pgp_encrypt", "public_key": "APPLERSA_087CFD3654FEE294.asc", "folder_from": "/cdr/bigmerchant/temp/settlement/", "folder_to": "/cdr/bigmerchant/outbox/settlement/", "folder_backup": "/cdr/bigmerchant/backup/settlement/", "file_format": "^O_(\\d{14})_(.+)_SETTLEMENT_DETAIL.csv$", "backup": "yes", "file_extend": ".gpg"}, {"state": "active", "name": "KBANK_DISPUTE_IN", "type": "pgp_decrypt", "private_key": "kbank_pgp_pri_key.asc", "passphrase": "4n8c8f5T", "file_extend": ".pgp", "folder_from": "/cdr/kbankpgw/PGW/inbox/", "folder_to": "/cdr/merchantfile/kbankdispute/inbox/", "folder_backup": "/cdr/kbankpgw/backup/inbox/", "file_format": "(.pgp)$", "backup": "yes"}, {"state": "active", "name": "KBANK_ONBOARD_OUT", "type": "pgp_encrypt", "public_key": "kbankupos_pgp_pub_key.asc", "folder_from": "/cdr/kbankupos/onboard/temp/outbox/", "folder_to": "/cdr/kbankupos/outbox/", "folder_backup": "/cdr/kbankupos/onboard/temp/backup/", "file_format": ".+", "backup": "yes", "file_extend": ".pgp"}, {"state": "active", "name": "KBANK_DISPUTE_OUT", "type": "pgp_encrypt", "public_key": "kbankupos_pgp_pub_key.asc", "folder_from": "/cdr/merchantfile/kbankdispute/outbox/", "folder_to": "/cdr/kbankupos/outbox/", "folder_backup": "/cdr/merchantfile/kbankdispute/backup/outbox/", "file_format": ".+", "backup": "yes", "file_extend": ".pgp"}, {"state": "active", "name": "EXP_BIGMERCHANT_INSTALLMENT_SETTLEMENT_GPG", "type": "pgp_encrypt", "public_key": "APPLERSA_087CFD3654FEE294.asc", "folder_from": "/cdr/bigmerchant/temp/ita_settlement/", "folder_to": "/cdr/bigmerchant/outbox/ita_settlement/", "folder_backup": "/cdr/bigmerchant/backup/outbox/ita_settlement/", "file_format": "^O_(\\d{14})_(.+)_SETTLEMENT_DETAIL.csv$", "backup": "yes", "file_extend": ".gpg"}, {"state": "active", "name": "EXP_BIGMERCHANT_INSTALLMENT_SETTLEMENT", "type": "sftp", "traffic": "out", "host": "b2b-sftp.apple.com", "port": 22, "user_name": "onepay", "password": "", "private_key": ["-----BEGIN RSA PRIVATE KEY-----", "MIIEowIBAAKCAQEA0e36VB5Nzeh2mULzM1akQ+DxwFBNYqAHediYK0J4u+zcWbyQ", "ddOJT2zSgAtFUdq0bTRe5mWQ5JHT0TU1yeTsg9eL2pS+WTxPzwfiZxUc2uPXL9ft", "21hmMbXizOdR/tMaC4DM8KLBRtXLP1eaSciy47FBifT4l1rdV3MEGxBnKH9rkhWY", "hNzBFryY6ear1G9iOSq6jjcyARRkWUAzr+nleUoan2jpy8LMaNYuYEep/91wOHrv", "pY01kKVzJeTjPAGEJ2fPHhG/sRf9MrdJo9NpISMzH3vXm77PI2IKjNaFab1KdGz7", "DH4VW0pJnep4a4VShQtBa8ImcP6IES7B2zSx1wIDAQABAoIBAD86eSo60l5RRN9W", "bd+pmeIj70xoI1awMnA9j1+ALtMTPS79/1ittO6qLSCJxIAp6iv2GRs1vhVJ4s1h", "RGkhvSbcle/o2wgPhU4q5sL7dexrsVVkqOxmzKjuSnhCt8yTkoWibpePvbzzCbtz", "TDIq885mhgHiObmEkaQULUllgFyOr/6epqqsALff0MqNjROZdKpAqfPieLetHi6R", "Jf1Nu5mh/pofAQA/2nnbq/bmhN74fsRmkv/v+JmrzcDTQnzVXXBw45NCaNiDrIhj", "pJ0xdRpysXTRlhsh/XQP8rC7HF490nukUZOpR0IColoJrs7z9y7rRvLz8wX+qwDD", "sMZ05ckCgYEA/juk5WVxw6edKAlk4ZyM9WPVv2L2ZaIomLeElncbZ4ZNfEVjwN/w", "GeZHBKhTYJ5rNNAZ+xY7r0R52yC1VJ0dyLwRBtinSr7Sbn35ZzbRKenLM14fWotE", "Jpfnc6evxFEOcxlh5Osc0PKM6zmKpAfrUYqqUeY0EoZsPZW2myGItDMCgYEA02OB", "PqeZbYQS/qo9PUDqyXADDT2wnIMox1fRHCVlLtT+gk+FKi0IyFqcreF5uu7bsDxv", "OrDeyqnASzH7PC/ZSc+qM0dL7QREK0vA3gtZcsUqRU2BpXrt8oiiJhsRL2Uy9mk1", "Uw9oDRrBzKyCQ9aq7UrO1ri0/7KtkLfYXPX1B80CgYEAuwSBLsbLQ1R/xT3z2gth", "nfpM+LcB7o21M3J8tHr1y9f+FBavZOjXJRZC0+hHPWIMFxOmqRAX47yJr2qGObJC", "gLHoDJM/rTpmbiV5XDX089IqSqVIh0C/Z2yDNI78O3wtFvprCwzxa7uhckAn/rZc", "ouVQmWKwl5gO89a4N7IZcAUCgYB3E0U5uwVFWLGufM00VP4s7Ij7i3WSJJvy9rfX", "aAVARUD8vLihkm6xFcHOfQWqVGSyOMjbQciuVsL4WLZSKA35cx5H9nCBmfaggPKq", "4a/IY+I02b0fL0kcZvUYDAU5MoNrpMYh/Lq7LwheOArFFgaA1rP+5lsm8kqtC7K4", "YuBdPQKBgB/B0wdXmRv5Gi8Kc6/hNaW9JeViqHlDdCmHL2E5BGwIcVjUx7BMwVmL", "LaDdBgwkYaJoclib4FWCYmlESQGEmEXbse8yHp6bs8ufigJciQFpANNz5CMrHvp6", "T/d3YflMApYnlMtWDcPNW3Ys+W9OR8IzkdzuSegrRS80D78XeofA", "-----END RSA PRIVATE KEY-----"], "folder_from": "/cdr/bigmerchant/outbox/ita_settlement/", "folder_to": "/out/cci/settlement/", "folder_backup": "/cdr/bigmerchant/backup/outbox/ita_settlement/", "file_format": "O_(\\d{14})_(.+)_SETTLEMENT_DETAIL.csv.gpg$", "backup": "yes"}, {"state": "active", "name": "EXP_BIGMERCHANT_CONVERSION_REPORT_GPG", "type": "pgp_encrypt", "public_key": "APPLERSA_087CFD3654FEE294.asc", "folder_from": "/cdr/bigmerchant/temp/conversion_report/", "folder_to": "/cdr/bigmerchant/outbox/conversion_report/", "folder_backup": "/cdr/bigmerchant/backup/outbox/conversion_report/", "file_format": "^O_(\\d{14})_(.+)_CONVERSION_REPORT.csv$", "backup": "yes", "file_extend": ".gpg"}, {"state": "active", "name": "EXP_BIGMERCHANT_CONVERSION_REPORT", "type": "sftp", "traffic": "out", "host": "b2b-sftp.apple.com", "port": 22, "user_name": "onepay", "password": "", "private_key": ["-----BEGIN RSA PRIVATE KEY-----", "MIIEowIBAAKCAQEA0e36VB5Nzeh2mULzM1akQ+DxwFBNYqAHediYK0J4u+zcWbyQ", "ddOJT2zSgAtFUdq0bTRe5mWQ5JHT0TU1yeTsg9eL2pS+WTxPzwfiZxUc2uPXL9ft", "21hmMbXizOdR/tMaC4DM8KLBRtXLP1eaSciy47FBifT4l1rdV3MEGxBnKH9rkhWY", "hNzBFryY6ear1G9iOSq6jjcyARRkWUAzr+nleUoan2jpy8LMaNYuYEep/91wOHrv", "pY01kKVzJeTjPAGEJ2fPHhG/sRf9MrdJo9NpISMzH3vXm77PI2IKjNaFab1KdGz7", "DH4VW0pJnep4a4VShQtBa8ImcP6IES7B2zSx1wIDAQABAoIBAD86eSo60l5RRN9W", "bd+pmeIj70xoI1awMnA9j1+ALtMTPS79/1ittO6qLSCJxIAp6iv2GRs1vhVJ4s1h", "RGkhvSbcle/o2wgPhU4q5sL7dexrsVVkqOxmzKjuSnhCt8yTkoWibpePvbzzCbtz", "TDIq885mhgHiObmEkaQULUllgFyOr/6epqqsALff0MqNjROZdKpAqfPieLetHi6R", "Jf1Nu5mh/pofAQA/2nnbq/bmhN74fsRmkv/v+JmrzcDTQnzVXXBw45NCaNiDrIhj", "pJ0xdRpysXTRlhsh/XQP8rC7HF490nukUZOpR0IColoJrs7z9y7rRvLz8wX+qwDD", "sMZ05ckCgYEA/juk5WVxw6edKAlk4ZyM9WPVv2L2ZaIomLeElncbZ4ZNfEVjwN/w", "GeZHBKhTYJ5rNNAZ+xY7r0R52yC1VJ0dyLwRBtinSr7Sbn35ZzbRKenLM14fWotE", "Jpfnc6evxFEOcxlh5Osc0PKM6zmKpAfrUYqqUeY0EoZsPZW2myGItDMCgYEA02OB", "PqeZbYQS/qo9PUDqyXADDT2wnIMox1fRHCVlLtT+gk+FKi0IyFqcreF5uu7bsDxv", "OrDeyqnASzH7PC/ZSc+qM0dL7QREK0vA3gtZcsUqRU2BpXrt8oiiJhsRL2Uy9mk1", "Uw9oDRrBzKyCQ9aq7UrO1ri0/7KtkLfYXPX1B80CgYEAuwSBLsbLQ1R/xT3z2gth", "nfpM+LcB7o21M3J8tHr1y9f+FBavZOjXJRZC0+hHPWIMFxOmqRAX47yJr2qGObJC", "gLHoDJM/rTpmbiV5XDX089IqSqVIh0C/Z2yDNI78O3wtFvprCwzxa7uhckAn/rZc", "ouVQmWKwl5gO89a4N7IZcAUCgYB3E0U5uwVFWLGufM00VP4s7Ij7i3WSJJvy9rfX", "aAVARUD8vLihkm6xFcHOfQWqVGSyOMjbQciuVsL4WLZSKA35cx5H9nCBmfaggPKq", "4a/IY+I02b0fL0kcZvUYDAU5MoNrpMYh/Lq7LwheOArFFgaA1rP+5lsm8kqtC7K4", "YuBdPQKBgB/B0wdXmRv5Gi8Kc6/hNaW9JeViqHlDdCmHL2E5BGwIcVjUx7BMwVmL", "LaDdBgwkYaJoclib4FWCYmlESQGEmEXbse8yHp6bs8ufigJciQFpANNz5CMrHvp6", "T/d3YflMApYnlMtWDcPNW3Ys+W9OR8IzkdzuSegrRS80D78XeofA", "-----END RSA PRIVATE KEY-----"], "folder_from": "/cdr/bigmerchant/outbox/conversion_report/", "folder_to": "/out/cci/conversion_report/", "folder_backup": "/cdr/bigmerchant/backup/outbox/conversion_report/", "file_format": "O_(\\d{14})_(.+)_CONVERSION_REPORT.csv.gpg$", "backup": "yes"}, {"state": "active", "name": "MSB_DIRECT_DEBIT", "type": "sftp", "traffic": "in", "host": "*************", "port": 2222, "user_name": "onepay_user", "password": "Onepay@1234", "private_key": [], "folder_from": "/IN/", "folder_to": "/cdr/msb/direct-debit/inbox/", "folder_backup": "/IN/backup/", "file_format": "BC_chi_tiet_giao_dich_vi_dien_tu_.+.xlsx", "backup": "yes", "skip_folder": "true"}, {"state": "active", "name": "MSB_PAYCOLLECT", "type": "sftp", "traffic": "in", "host": "*************", "port": 2222, "user_name": "onepay_user", "password": "Onepay@1234", "private_key": [], "folder_from": "/IN/", "folder_to": "/cdr/msb/paycollect/inbox/", "folder_backup": "/IN/backup/", "file_format": "BC_Chi_Tiet_Giao_Dich_VA_.+.xlsx", "backup": "yes", "skip_folder": "true"}, {"state": "active", "name": "UPLOAD_GSM_RECONCILIATION", "type": "upload_aws", "access_key": "********************", "secret": "2qLFf8NJvAymR0+cCEoim9xkU0/E5TljYtXEZGyj", "folder_from": "/cdr/gsm/outbox/", "bucket_folder": "onepay-input", "bucket": "gsm-reconcile", "folder_backup": "/cdr/gsm/backup/outbox/", "region": "ap-southeast-1", "file_format": "^onepay_transaction_(\\d{8}).csv$"}, {"state": "active", "name": "UPLOAD_GSM_SALE_REPORT", "type": "upload_aws", "access_key": "********************", "secret": "2qLFf8NJvAymR0+cCEoim9xkU0/E5TljYtXEZGyj", "folder_from": "/cdr/gsm/outbox/", "bucket_folder": "onepay-sale-report", "bucket": "gsm-reconcile", "folder_backup": "/cdr/gsm/backup/outbox/", "region": "ap-southeast-1", "file_format": "^onepay_international_transaction_(\\d{8}).csv$"}, {"state": "active", "type": "local", "name": "VAB_ONECOM", "folder_from": "/cdr/vab/inbox/", "folder_to": "/cdr/vab/afterin/", "file_format": "^O_(\\d{8})_166888_PM.csv$"}, {"state": "active", "name": "DONGNAIPORT_ECOM_OUT", "type": "sftp", "traffic": "out", "host": "sftp.vietnamhub.vn", "port": 22, "user_name": "bidv", "password": "UEFJR05fQ0xJRU5UOm", "private_key": [], "folder_from": "/cdr/dongnaiport/outbox/", "folder_to": "/DNP/", "folder_backup": "/cdr/dongnaiport/backup/outbox/", "file_format": "BIDV_ONEPAY(\\d{8}).xlsx", "backup": "yes"}, {"state": "active", "name": "EXP_APPLE_DISPUTE_GPG", "type": "pgp_encrypt", "public_key": "APPLERSA_087CFD3654FEE294.asc", "folder_from": "/cdr/apple/dispute/", "folder_to": "/cdr/apple/outbox/dispute/", "folder_backup": "/cdr/apple/backup/dispute-gpg/", "file_format": "^VND_\\d{14}_INT_[A-Za-z0-9]+\\.csv$", "backup": "yes", "file_extend": ".gpg"}, {"state": "active", "name": "EXP_APPLE_DISPUTE", "type": "sftp", "traffic": "out", "host": "host bê<PERSON> <PERSON><PERSON><PERSON> vị", "port": "port bên đơn vị", "user_name": "root", "password": "4n8c8f5t", "private_key": [], "folder_from": "/cdr/apple/outbox/dispute/", "folder_to": "/out/apple/dispute/", "folder_backup": "/cdr/apple/backup/dispute-out/", "file_format": "^VND_\\d{14}_INT_[A-Za-z0-9]+\\.csv.gpg", "backup": "yes"}], "service_create_temp_file": [{"service": "EXP_BIGMERCHANT_SETTLEMENT", "enabled": true}]}
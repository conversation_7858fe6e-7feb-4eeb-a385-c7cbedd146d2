<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info" name="ssp-service" packages="" monitorInterval="30">
    <Appenders>
        <RollingFile name="RollingFile" fileName="${sys:log}"
                     filePattern="${sys:log}.%d{yyyy-MM-dd}.gz">
            <PatternLayout>
                <Pattern>%d %p %c{1.} [%t] %m%n</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <!--<SizeBasedTriggeringPolicy size="250 MB"/>-->
            </Policies>
        </RollingFile>
    </Appenders>
    <Loggers>
        <AsyncLogger name="vn.onepay" level="debug"/>
        <AsyncRoot level="info">
            <AppenderRef ref="RollingFile"/>
        </AsyncRoot>
    </Loggers>
</Configuration>
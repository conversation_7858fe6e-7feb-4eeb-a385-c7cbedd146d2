package vn.onepay.transfer.util;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.util.logging.Level;
import java.util.logging.Logger;

public class Util {
    private static Logger logger = Logger.getLogger(Util.class.getName());

    /**
     * @param accessKey access key to bucket
     * @param secret the access key's secret
     * @param srcPath (path + fileName) from local of the service 7290
     * @param bucket aws bucket
     * @param bucketFolder bucket's folder
     * @param fileName file name
     */
    public static boolean uploadAWS(String accessKey, String secret, String srcPath, String bucket, String bucketFolder, String fileName, String region) {
        if (configureAws(accessKey, secret, region)) {
            return uploadAWS(srcPath, bucket, bucketFolder, fileName);
        }
        return false;
    }

    private static boolean uploadAWS(String srcPath, String bucket, String folder, String fileName) {
        logger.info("START UPLOAD FILE AWS");
        String s3Command = "aws s3 cp " + srcPath + " s3://" + bucket + "/" + folder + "/";
        // String checkCommand = "aws s3api list-objects --query \'Contents[].Key\' --bucket gsm-reconcile";
        String checkCommand = "aws s3api head-object --bucket " + bucket + " --key " + folder + "/" + fileName;
        try {
            // Execute the S3 upload command
            boolean uploadSuccess = executeCommand(s3Command);
            if (uploadSuccess) {
                logger.info("Upload succeeded. Now checking the file.");
            } else {
                logger.info("Upload failed.");
                return false;
            }

            // Execute the check command
            boolean checkSuccess = executeCommand(checkCommand);
            if (checkSuccess) {
                logger.info(() -> "File check succeeded: " + fileName);
                return true;
            } else {
                logger.info("File check failed.");
                return false;
            }

        } catch (Exception e) {
            logger.log(Level.SEVERE, " ", e);
            return false;
        }
    }

    private static boolean executeCommand(String command) throws IOException, InterruptedException {
        Process process = Runtime.getRuntime().exec(command);
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
            boolean hasError = false;
            String line;
            while ((line = reader.readLine()) != null) {
                logger.info("line: " + line);
            }

            String lineFault;
            while ((lineFault = errorReader.readLine()) != null) {
                logger.info("error: " + lineFault);
                hasError = true;
            }

            int exitCode = process.waitFor();
            if (exitCode != 0 || hasError) {
                logger.info(command + "\nexecuteCommand failed with exit code: " + exitCode);
                return false;
            }
        }
        return true;
    }

    private static boolean configureAws(String accessKey, String secret, String region) {
        try {
            logger.info("begin configureAws");
            // Create a ProcessBuilder instance
            ProcessBuilder processBuilder = new ProcessBuilder("aws", "configure");
            // Start the process
            Process process = processBuilder.start();

            // Provide the inputs to the process
            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()))) {
                writer.write(accessKey + "\n");
                writer.write(secret + "\n");
                writer.write(region + "\n");
                writer.write("text\n");
                writer.flush();
            }

            // Wait for the process to complete
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                logger.info("configureAws failed with exit code " + exitCode);
                return false;
            }
            logger.info("end configureAws");
            String checkCommand = "aws configure list";
            executeCommand(checkCommand);
            return true;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "configureAws failed", e);
            return false;
        }
    }

}

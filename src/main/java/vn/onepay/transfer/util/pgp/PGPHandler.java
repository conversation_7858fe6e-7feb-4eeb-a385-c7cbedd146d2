package vn.onepay.transfer.util.pgp;

import java.util.logging.Level;
import java.util.logging.Logger;

public class PGPHandler {
    private static final Logger logger = Logger.getLogger(PGPHandler.class.getName());

    public static String decryptPGP(String inputFile, String keyFile, String outputFile, String pass) {
        try {
            PGPFileProcessor fileProcessor = new PGPFileProcessor();
            logger.info(() -> "Input: " + inputFile + " Output: " + outputFile + " KeyFile: " + keyFile);
            fileProcessor.setInputFile(inputFile);
            fileProcessor.setKeyFile(keyFile);
            fileProcessor.setOutputFile(outputFile);
            fileProcessor.setPassphrase(pass);
            if (fileProcessor.decrypt()) {
                return fileProcessor.getOutputFile();
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error: ", e);
        }
        return "";
    }

    public static String encryptPGP(String inputFile, String keyFile, String outputFile, String pass) {
        try {
            PGPFileProcessor fileProcessor = new PGPFileProcessor();
            logger.info(() -> "Input: " + inputFile + " Output: " + outputFile + " KeyFile: " + keyFile);
            fileProcessor.setInputFile(inputFile);
            fileProcessor.setKeyFile(keyFile);
            fileProcessor.setOutputFile(outputFile);
            fileProcessor.setPassphrase(pass);
            if (fileProcessor.encrypt()) {
                return fileProcessor.getOutputFile();
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error: ", e);
        }
        return "";
    }
}

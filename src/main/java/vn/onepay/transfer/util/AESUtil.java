package vn.onepay.transfer.util;

import javax.crypto.spec.*;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import javax.crypto.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.nio.file.*;
import java.io.*;

public class AESUtil {
    public static String decrypt(final String key, final String initVector, final String encrypted) {
        try {
            final IvParameterSpec iv = new IvParameterSpec(initVector.getBytes("UTF-8"));
            final SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "AES");
            final Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(2, skeySpec, iv);
            byte[] byteencrypted = null;
            try {
                byteencrypted = Base64.getDecoder().decode(encrypted);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            final byte[] original = cipher.doFinal(byteencrypted);
            return new String(original);
        } catch (Exception ex2) {
            ex2.printStackTrace();
            return null;
        }
    }

//    private static String getFile(final String sFile) {
//        String everything = "";
//        try {
//            everything = new String(Files.readAllBytes(Paths.get(sFile, new String[0])));
//        } catch (Exception ex) {
//            everything = ex.toString();
//        }
//        return everything;
//    }

//    private static void writeFile(final String sFile, final String sContent) {
//        try {
//            final FileWriter myWriter = new FileWriter(sFile);
//            myWriter.write(sContent);
//            myWriter.close();
//            System.out.println("Successfully wrote to the file.");
//        } catch (IOException e) {
//            System.out.println("An error occurred.");
//            e.printStackTrace();
//        }
//    }

    public static void main(final String[] args) {
        // try {
        //     getSftpAgribank("**************", 22, "root", "4n8c8f5t",null,
        //      "/cdr/agribank/onecom/temp1/","/cdr/agribank/onecom/temp2/",
        //       "20221121_SETTLE.dec", "N8yiEnqBc8TFK3OX" , "****************");
        // } catch (Exception e) {
        //     e.printStackTrace();
        // }
    }

//    private static boolean getSftpAgribank(String host, int port, String user, String pass, String privateKey,String foldFrom, String foldTo, String regex, String key, String initVector) throws Exception {
//        JSch jsch = new JSch();
//        if (privateKey != null && !"".equals(privateKey)) {
//            jsch.addIdentity("", privateKey.getBytes(), null, null);
//        }
//
//        Session session = jsch.getSession(user, host, port);
//        session.setConfig("StrictHostKeyChecking", "no");
//        if (pass != null && !"".equals(pass)) {
//            session.setPassword(pass);
//        }
//
//        session.connect();
//
//        Channel channel = session.openChannel("sftp");
//        channel.connect();
//        ChannelSftp sftpChannel = (ChannelSftp) channel;
//
//        scanFolder(sftpChannel, foldFrom, foldTo, null, regex, key, initVector);
//
//        sftpChannel.disconnect();
//        session.disconnect();
//        return true;
//    }

//    private static void scanFolder(ChannelSftp sftpChannel, String foldFrom, String folderTo, String folderBackup, String regex, String key, String initVector) throws Exception {
//        sftpChannel.cd(foldFrom);
//        Vector<ChannelSftp.LsEntry> list = sftpChannel.ls("*");
//        for (ChannelSftp.LsEntry entry : list) {
//            final String fn = entry.getFilename();
//            if (entry.getAttrs().isDir()) {
//                scanFolder(sftpChannel, entry.getFilename(), folderTo, folderBackup, regex, key, initVector);
//            } else {
//                String fileName = entry.getFilename();
//                Pattern pattern = Pattern.compile(regex);
//                Matcher matcher = pattern.matcher(fileName);
//                if (matcher.find()) {
//                    InputStream in = sftpChannel.get(entry.getFilename());
//                    byte[] data = readBytes(in);
//                    byte[] dataDecoded = decrypt(key, initVector, new String(data)).getBytes();
//                    sftpChannel.cd(folderTo);
//                    OutputStream out = sftpChannel.put(fileName.replace(".dec", ".txt"));
//                    out.write(dataDecoded);
//                    out.close();
//                    in.close();
//                }
//            }
//        }
//        sftpChannel.cd("..");
//    }

//    private static byte[] readBytes(InputStream inputStream) throws IOException {
//        byte[] b = new byte[1024];
//        ByteArrayOutputStream os = new ByteArrayOutputStream();
//        int c;
//        while ((c = inputStream.read(b)) != -1) {
//            os.write(b, 0, c);
//        }
//        return os.toByteArray();
//    }
}

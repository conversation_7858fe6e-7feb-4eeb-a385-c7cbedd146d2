package vn.onepay.transfer;

import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonObject;
import vn.onepay.transfer.job.TaskTrigger;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * Created by thieuchivuong on 8/22/17.
 */
public class AppVerticle extends AbstractVerticle {
    private static final Logger logger = Logger.getLogger(AppVerticle.class.getName());

    public static JsonObject config;

    @Override
    public void start(Future<Void> startFuture) throws Exception {
        try {
            InputStream inputStream = AppVerticle.class.getClassLoader().getResourceAsStream("config.json");
            String result = new BufferedReader(new InputStreamReader(inputStream))
                    .lines().collect(Collectors.joining("\n"));

            config = new JsonObject(result);
            String timeRegex = config.getJsonObject("schedule").getString("time_regex");
            logger.info("Init schedule:" + timeRegex);
            TaskTrigger trigger = new TaskTrigger(timeRegex);
            trigger.init();
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "", ex);
        }
    }


}


//package vn.onepay.transfer.pgp;
//
//import com.didisoft.pgp.KeyStore;
//import com.didisoft.pgp.PGPLib;
//
///**
// * This example demonstrates how to encrypt file using public key located in KeyStore.
// */
//public class KeystoreEncryptFile {
//	public static void main(String[] args) throws Exception{
//
//		// create an instance of the KeyStore
//		KeyStore keyStore = new KeyStore("/home/<USER>/projects/onepay/cdr-transfer/src/main/service/datafiles/pgp.keystore", "changeit");
//
//		// create an instance of the library
//		PGPLib pgp = new PGPLib();
//
//		// String recipientUserId = "<EMAIL>";
//		String recipientUserId = "<EMAIL>";
//
//		// is output ASCII or binary
//        boolean asciiArmor = true;
//        // should integrity check information be added
//        // set to false for compatibility with older versions of PGP such as 6.5.8.
//        boolean withIntegrityCheck = true;
//
//		pgp.encryptFile("/home/<USER>/projects/onepay/cdr-transfer/src/main/service/datafiles/pm.dat",
//						keyStore,
//						recipientUserId,
//						"/home/<USER>/projects/onepay/cdr-transfer/src/main/service/datafiles/pm.pgp",
//						asciiArmor,
//						withIntegrityCheck);
//	}
//}

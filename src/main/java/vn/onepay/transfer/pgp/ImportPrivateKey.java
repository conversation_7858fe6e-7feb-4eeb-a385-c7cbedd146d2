//package vn.onepay.transfer.pgp;
//
//import com.didisoft.pgp.KeyStore;
//
///**
// * This example demonstrates how to import an existing OpenPGP file
// * into a KeyStore file.
// *
// * Using a keyStore gives us additional layer of security.
// */
//public class ImportPrivateKey {
//	public static void main(String[] args) throws Exception {
//		// initialize the KeyStore. The key store file may not exist
//		// and subsequent operations will create it
//		KeyStore keyStore = new KeyStore("/home/<USER>/projects/onepay/cdr-transfer/src/main/service/test/pgp.keystore", "changeit");
//
//		// import the key
//		keyStore.importPrivateKey("/home/<USER>/projects/onepay/cdr-transfer/src/main/service/test/exported_sec.asc", "changeit");
//	}
//}

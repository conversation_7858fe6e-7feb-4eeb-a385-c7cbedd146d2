//package vn.onepay.transfer.pgp;
//
//import com.didisoft.pgp.KeyStore;
//
///**
// * This example demonstrates how to export a public key only from a KeyStore file.
// *
// * The method refers the public key by User Id,
// * but also an overloaded version exists that accepts Key Id.
// */
//public class ExportPublicKey {
//	public static void main(String[] args) throws Exception{
//		// initialize the key store
//		KeyStore keyStore = new KeyStore("/home/<USER>/projects/onepay/cdr-transfer/src/main/service/datafiles/onepay.keystore", "changeit");
//
//		// should output be ASCII or binary
//		boolean asciiArmor = true;
//
//		// export
//		keyStore.exportPublicKey("/home/<USER>/projects/onepay/cdr-transfer/src/main/service/datafiles/onepay_pub.asc", "<EMAIL>", asciiArmor);
//	}
//}

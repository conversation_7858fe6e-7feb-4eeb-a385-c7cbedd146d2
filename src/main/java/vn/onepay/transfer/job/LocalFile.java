package vn.onepay.transfer.job;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import java.io.*;
import java.nio.file.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LocalFile {
    private static Log logger = LogFactory.getLog(LocalFile.class.getName());

    public static void moveFileLocal(String folderScan, String fileFormat, String folderTo) throws Exception {
        File folder = new File(folderScan);
        for (File item : folder.listFiles()) {
            if (item.isDirectory()) {
                moveFileLocal(item.getPath(), fileFormat, folderTo);
            } else {
                String fileName = item.getName();
                logger.info("Process file " + fileName + ", file format=" + fileFormat);

                Pattern pattern = Pattern.compile(fileFormat);
                Matcher matcher = pattern.matcher(item.getName());

                if (matcher.find()) {
                    try {
                        logger.info("folderScan:" + item.getPath() + ";folderTo:" + folderTo);
                        Path fromPath = Paths.get(item.getPath());
                        Path toPath = Paths.get(folderTo + fileName);
                        Files.move(fromPath, toPath, StandardCopyOption.REPLACE_EXISTING);
                    } catch (Exception e) {// Catch exception if any
                        logger.error(e.getMessage(), e);
                    }
                } else {
                    logger.info("file not match format " + fileFormat);
                }
            }
        }
    }
}

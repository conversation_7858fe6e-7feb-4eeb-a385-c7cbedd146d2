package vn.onepay.transfer.job;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.net.ftp.*;

import java.io.*;
import java.util.Date;

import static vn.onepay.transfer.job.Task.readBytes;

public class Ftp {
    private static Log logger = LogFactory.getLog(Ftp.class.getName());

    public static boolean pushFtp(String host, int port, String user, String pass, String folderExport, String fileName, byte[] data) throws Exception {
        FTPClient ftp = new FTPClient();
        ftp.connect(host, port);
        if (ftp.isConnected()) {
            ftp.login(user, pass);
            ftp.setFileType(FTP.BINARY_FILE_TYPE);
            InputStream in = new ByteArrayInputStream(data);
            ftp.storeFile(getFullPath(folderExport, fileName), in);
            boolean success = ftp.completePendingCommand();
            if (success) {
                logger.info(fileName + " pushed successfully");
            }
            in.close();
            ftp.disconnect();
        }
        return true;
    }

    public static boolean pushFtp(String host, int port, String user, String pass, String folderFrom, String folderTo, String folderBackup) throws Exception {
        FTPClient ftp = new FTPClient();
        ftp.connect(host, port);
        if (ftp.isConnected()) {
            ftp.login(user, pass);
//            logger.info("System Type: " + ftp.getSystemType());
            ftp.setFileType(FTP.ASCII_FILE_TYPE);
            ftp.enterLocalPassiveMode();
            ftp.setUseEPSVwithIPv4(true);

            showServerReply(ftp);
            ftp.sendCommand(FTPCmd.PWD);
            showServerReply(ftp);

            ftp.sendCommand(FTPCmd.CHANGE_WORKING_DIRECTORY, folderTo);
            showServerReply(ftp);

            ftp.sendCommand(FTPCmd.PWD);
            showServerReply(ftp);
            File folder = new File(folderFrom);
            String[] names = folder.list();
            logger.info("total files:" + names.length);
            for (String fileName : names) {
                logger.info("file name:" + fileName);
                InputStream in = new FileInputStream(getFullPath(folderFrom, fileName));
                ftp.storeFile(getFullPath(folderTo, fileName), in);
                in.close();
                if (folderBackup != null && !folderBackup.equalsIgnoreCase("")) {
                    File file1 = new File(getFullPath(folderFrom, fileName));
                    File file2 = new File(getFullPath(folderBackup, fileName));
                    if (file2.exists()) file2.deleteOnExit();
                    boolean success = file1.renameTo(file2);
                    if (success) {
                        logger.info(fileName + " backup successfully");
                    } else {
                        logger.info(fileName + "can not backup so create new backup file");
                        OutputStream outStream = new FileOutputStream(file2);
                        InputStream fileStream = new FileInputStream(file1);
                        byte[] data = readBytes(fileStream);
                        outStream.write(data);
                        file1.deleteOnExit();
                        try {
                            outStream.close();
                        } catch (IOException e) {
                            logger.error(e);
                        }

                        try {
                            fileStream.close();
                        } catch (IOException e) {
                            logger.error(e);
                        }
                    }
                }
            }
            ftp.disconnect();
        }
        return true;
    }

    public static boolean getFtp(String host, int port, String user, String pass, String folderFrom, String folderTo, String folderBackup, String folderCheckExist) throws Exception {
        FTPClient ftp = new FTPClient();
        ftp.connect(host, port);
        if (ftp.isConnected()) {
            ftp.login(user, pass);
//            ftp.setDataTimeout(3000); // 3 seconds
//            ftp.setConnectTimeout(3000); // 3 seconds

            logger.info("System Type: " + ftp.getSystemType());
            ftp.setFileType(FTP.ASCII_FILE_TYPE);

//            ftp.sendCommand(FTPCmd.PASV);
            ftp.enterLocalPassiveMode();
            ftp.setUseEPSVwithIPv4(true);

            showServerReply(ftp);
            ftp.sendCommand(FTPCmd.PWD);
            showServerReply(ftp);

            ftp.sendCommand(FTPCmd.CHANGE_WORKING_DIRECTORY, folderFrom);
            showServerReply(ftp);

            ftp.sendCommand(FTPCmd.PWD);
            showServerReply(ftp);

            String[] names = ftp.listNames();
            logger.info("total files:" + names.length);
            for (String fileName : names) {
                File eFile1 = new File(getFullPath(folderCheckExist, fileName));
                File eFile2 = new File(getFullPath(folderTo, fileName));
                if (eFile1.exists() || eFile2.exists()) {
                    logger.info("file name:" + fileName + " existed");
                } else {
                    logger.info("file name:" + fileName + " will be downloaded");
                    InputStream in = ftp.retrieveFileStream(getFullPath(folderFrom, fileName));
                    if (in != null) {
                        File lFile = new File(getFullPath(folderTo, fileName));
                        OutputStream out = new BufferedOutputStream(new FileOutputStream(lFile));
                        byte[] bytesArray = new byte[4096];
                        int bytesRead = -1;
                        while ((bytesRead = in.read(bytesArray)) != -1) {
                            out.write(bytesArray, 0, bytesRead);
                        }
                        boolean success = ftp.completePendingCommand();
                        if (success) {
                            logger.info(fileName + " downloaded successfully");
                            out.close();
                        }
                    }
                    if (folderBackup != null && !folderBackup.equalsIgnoreCase("")) {
                        boolean blBackupFile = ftp.rename(fileName, getFullPath(folderBackup, fileName));
                        if (blBackupFile) logger.info(fileName + " backup successfully");
                    }
                    if (in != null) in.close();

                }
            }
            ftp.disconnect();
        }
        return true;
    }

    private static String getFullPath(String path, String fileName) {
        String startSeparator = path.startsWith("/") ? "" : "/";
        String endSeparator = path.endsWith("/") ? "" : "/";
        return startSeparator + path + endSeparator + fileName;
    }

    private static void showServerReply(FTPClient ftpClient) {
        String[] replies = ftpClient.getReplyStrings();
        if (replies != null && replies.length > 0) {
            for (String aReply : replies) {
                logger.info("Server reply: " + aReply);
            }
        }

    }
}

package vn.onepay.transfer.job;

import org.quartz.*;

import java.util.logging.Level;
import java.util.logging.Logger;

public class TaskTrigger {
    private static final Logger logger = Logger.getLogger(TaskTrigger.class.getName());

    private String schedule;

    public TaskTrigger(String schedule) {
        this.schedule = schedule;
    }

    public void init() {
        try {
            JobDetail jobDetail = JobBuilder.newJob(Task.class)
                    .withIdentity("Transfer", "OnePAY").build();
            Trigger trigger = TriggerBuilder
                    .newTrigger()
                    .withIdentity("Transfer", "OnePAY")
                    .withSchedule(CronScheduleBuilder.cronSchedule(schedule))
                    .build();
            AppScheduler.getScheduler().scheduleJob(jobDetail, trigger);
        } catch (Exception ex) {
            logger.log(Level.SEVERE, ex.getMessage());
        }
    }
}

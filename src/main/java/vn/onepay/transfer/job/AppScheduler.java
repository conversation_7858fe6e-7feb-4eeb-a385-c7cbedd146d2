package vn.onepay.transfer.job;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.quartz.Scheduler;
import org.quartz.impl.StdSchedulerFactory;

public class AppScheduler {
    public static Scheduler scheduler;
    private static Log logger = LogFactory.getLog(AppScheduler.class.getName());

    static {
        try {
            scheduler = new StdSchedulerFactory().getScheduler();
            scheduler.start();
        } catch (Exception ex) {
            logger.error(ex);
        }
    }

    public static Scheduler getScheduler() {
        return scheduler;
    }
}

package vn.onepay.transfer.job;

import com.jcraft.jsch.*;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import java.io.*;
import java.util.Vector;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Sftp {
    private static Log logger = LogFactory.getLog(Sftp.class.getName());

    public static boolean pushSftp(String host, int port, String user, String pass, String privateKey, String folderExport, String fileName, byte[] data, boolean isCreateTmp) throws Exception {
        JSch jsch = new JSch();
        if (privateKey != null && !"".equals(privateKey)) {
            jsch.addIdentity("", privateKey.getBytes(), null, null);
        }

        Session session = jsch.getSession(user, host, port);
        session.setConfig("StrictHostKeyChecking", "no");
        session.setConfig("server_host_key", session.getConfig("server_host_key") + ",ssh-rsa");
        session.setConfig("PubkeyAcceptedAlgorithms", session.getConfig("PubkeyAcceptedAlgorithms") + ",ssh-rsa");
        if (pass != null && !"".equals(pass)) {
            session.setPassword(pass);
        }

        session.connect();

        Channel channel = session.openChannel("sftp");
        channel.connect();
        ChannelSftp sftpChannel = (ChannelSftp) channel;
        // logger.info("sftpChannel.cd(folderExport="+folderExport+")");
        sftpChannel.cd(getFullPath(folderExport, ""));
        sftpChannel.cd(folderExport);
        InputStream in = new ByteArrayInputStream(data);
        if (isCreateTmp) {
            sftpChannel.put(in, fileName + ".tmp", ChannelSftp.OVERWRITE);
            sftpChannel.rename(fileName + ".tmp", fileName);
        } else {
            sftpChannel.put(in, fileName, ChannelSftp.OVERWRITE);
        }
        sftpChannel.disconnect();
        session.disconnect();
        in.close();
        return true;
    }

    public static boolean getSftp(String host, int port, String user, String pass, String privateKey, String folderFrom, String folderTo, String folderBackup, String fileFormat, String skipFolder) throws Exception {
        JSch jsch = new JSch();
        if (privateKey != null && !"".equals(privateKey)) {
            jsch.addIdentity("", privateKey.getBytes(), null, null);
        }

        Session session = jsch.getSession(user, host, port);
        session.setConfig("StrictHostKeyChecking", "no");
        session.setConfig("server_host_key", session.getConfig("server_host_key") + ",ssh-rsa");
        session.setConfig("PubkeyAcceptedAlgorithms", session.getConfig("PubkeyAcceptedAlgorithms") + ",ssh-rsa");
        if (pass != null && !"".equals(pass)) {
            session.setPassword(pass);
        }

        session.connect();

        Channel channel = session.openChannel("sftp");
        channel.connect();
        ChannelSftp sftpChannel = (ChannelSftp) channel;
        try {
            scanFolder(sftpChannel, getFullPath(folderFrom, ""), folderTo, folderBackup, fileFormat, skipFolder);
        } catch (Exception ex) {
            logger.error("scanFolder error. ", ex);
        }
        sftpChannel.disconnect();
        session.disconnect();
        return true;
    }

    public static void scanFolder(ChannelSftp sftpChannel, String foldFrom, String folderTo, String folderBackup, String fileFormat, String skipFolder) throws Exception {
        sftpChannel.cd(foldFrom);
        Vector<ChannelSftp.LsEntry> list = sftpChannel.ls("*");
        for (ChannelSftp.LsEntry entry : list) {
            final String fileName = entry.getFilename();
            if (entry.getAttrs().isDir()) {
                if (skipFolder == null || !"true".equals(skipFolder)) {
                    logger.info("Scan " + fileName);
                    scanFolder(sftpChannel, entry.getFilename(), folderTo, folderBackup, fileFormat, skipFolder);
                } else {
                    logger.info("Skip " + fileName);
                }
            } else {
                Pattern pattern = Pattern.compile(fileFormat);
                Matcher matcher = pattern.matcher(entry.getFilename());
                if (matcher.find()) {
                    sftpChannel.get(fileName, getFullPath(folderTo, fileName));
                    if (folderBackup != null && !"".equalsIgnoreCase(folderBackup)) {
                        sftpChannel.rename(fileName, getFullPath(folderBackup, fileName));
                    }
                    logger.info("done move file " + fileName);
                }
            }
        }
        sftpChannel.cd("..");
    }

    private static String getFullPath(String path, String fileName) {
        String startSeparator = path.startsWith("/") ? "" : "/";
        String endSeparator = path.endsWith("/") ? "" : "/";
        return startSeparator + path + endSeparator + fileName;
    }
}

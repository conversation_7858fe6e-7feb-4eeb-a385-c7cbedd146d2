package vn.onepay.transfer.job;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.SchedulerException;
import vn.onepay.transfer.AppVerticle;
import java.io.*;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.text.SimpleDateFormat;
import vn.onepay.transfer.rsa.*;
import vn.onepay.transfer.util.AESUtil;
import vn.onepay.transfer.util.Util;
import vn.onepay.transfer.util.pgp.PGPHandler;

public class Task implements Job {

    private static Log logger = LogFactory.getLog(Task.class.getName());

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try {
            if (!isJobRunning(jobExecutionContext, "Transfer", "OnePAY")) {
                logger.info("=========START JOB===========");
                doJob();
            } else {
                logger.info("=========Job IS RUNNING===========");
            }
        } catch (Exception e) {
            logger.error("execute", e);
        }
        logger.info("=========END JOB===========");
    }

    public static boolean isJobRunning(JobExecutionContext ctx, String jobName, String groupName)
            throws SchedulerException {
        List<JobExecutionContext> currentJobs = ctx.getScheduler().getCurrentlyExecutingJobs();

        for (JobExecutionContext jobCtx : currentJobs) {
            String thisJobName = jobCtx.getJobDetail().getKey().getName();
            String thisGroupName = jobCtx.getJobDetail().getKey().getGroup();
            if (jobName.equalsIgnoreCase(thisJobName) && groupName.equalsIgnoreCase(thisGroupName)
                    && !jobCtx.getFireTime().equals(ctx.getFireTime())) {
                return true;
            }
        }
        return false;
    }

    private void doJob() {
        try {
            JsonArray partnerList = AppVerticle.config.getJsonArray("partners");
            JsonArray createTmpList = AppVerticle.config.getJsonArray("service_create_temp_file");
            for (int j = 0; j < partnerList.size(); j++) {
                JsonObject partner = partnerList.getJsonObject(j);
                String service = partner.getString("name");
                String traffic = partner.getString("traffic");
                String state = partner.getString("state");
                boolean isCreateTmp = false;
                for (int i = 0; i < createTmpList.size(); i++) {
                    JsonObject serviceTmp = createTmpList.getJsonObject(i);
                    if (serviceTmp.getString("service").equals(service) && Boolean.TRUE.equals(serviceTmp.getBoolean("enabled"))) {
                        isCreateTmp = true;
                    }
                }
                if ("sftp".equalsIgnoreCase(partner.getString("type")) && "out".equalsIgnoreCase(traffic)
                        && "active".equalsIgnoreCase(state)) {
                    processOutSftp(partner, isCreateTmp);
                } else if ("sftp".equalsIgnoreCase(partner.getString("type")) && "in".equalsIgnoreCase(traffic)
                        && "active".equalsIgnoreCase(state)) {
                    processInSftp(partner);
                } else if ("ftp".equalsIgnoreCase(partner.getString("type")) && "out".equalsIgnoreCase(traffic)
                        && "active".equalsIgnoreCase(state)) {
                    processOutFtp(partner);
                } else if ("ftp".equalsIgnoreCase(partner.getString("type")) && "in".equalsIgnoreCase(traffic)
                        && "active".equalsIgnoreCase(state)) {
                    processInFtp(partner);
                } else if ("rsa_encrypt".equalsIgnoreCase(partner.getString("type"))
                        && "active".equalsIgnoreCase(state)) {
                    processRsaEncrypt(partner);
                } else if ("rsa_decrypt".equalsIgnoreCase(partner.getString("type"))
                        && "active".equalsIgnoreCase(state)) {
                    processRsaDecrypt(partner);
                } else if ("pgp_encrypt".equalsIgnoreCase(partner.getString("type"))
                        && "active".equalsIgnoreCase(state)) {
                    processPgpEncrypt(partner);
                } else if ("pgp_decrypt".equalsIgnoreCase(partner.getString("type"))
                        && "active".equalsIgnoreCase(state)) {
                    processPgpDecrypt(partner);
                } else if ("aes_decrypt".equalsIgnoreCase(partner.getString("type"))
                        && "active".equalsIgnoreCase(state)) {
                    processAesDecrypt(partner);
                } else if ("upload_aws".equalsIgnoreCase(partner.getString("type"))
                        && "active".equalsIgnoreCase(state)) {
                    processUploadAws(partner);
                } else if ("local".equalsIgnoreCase(partner.getString("type")) 
                        && "active".equalsIgnoreCase(state)) {
                    processLocal(partner);
                } 
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
    }

    public static String getFolderNameAWS(String folderPath, String sDate) throws Exception {
        SimpleDateFormat sdfCreateDate = new SimpleDateFormat("dd/MM/yyyy");
        Date date = sdfCreateDate.parse(sDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String dateStr = sdf.format(calendar.getTime());
        return folderPath + "/" + dateStr;
  }

    private void processUploadAws(JsonObject partner) {
        try {
            logger.info("Partner Information:" + partner.toString());
            String folderFrom = partner.getString("folder_from");
            String bucketFolder = partner.getString("bucket_folder");
            String bucket = partner.getString("bucket");
            String accessKey = partner.getString("access_key");
            String secret = partner.getString("secret");
            String fileFormat = partner.getString("file_format");
            String region = partner.getString("region");
            File folder = new File(folderFrom);
            if (folder.isDirectory()) {
                for (File file : folder.listFiles()) {
                    if (!file.isFile())
                        continue;
                    logger.info((Object) ("Process file " + file.getName() + ", file format=" + fileFormat));
                    Pattern pattern = Pattern.compile(fileFormat);
                    String fileName = file.getName();
                    Matcher matcher = pattern.matcher(fileName);
                    if (matcher.find()) {
                        try {
                            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                            String sDate = sdf.format(new Date());
                            bucketFolder = getFolderNameAWS(bucketFolder, sDate);
                            if(Util.uploadAWS(accessKey, secret, file.getAbsolutePath(), bucket, bucketFolder, fileName, region)) {
                                String folderBackup = partner.getString("folder_backup");
                                File dir = new File(folderBackup);
                                File fileBackup = new File(dir, file.getName());
                                OutputStream outStream = new FileOutputStream(fileBackup);
                                InputStream inStream = new FileInputStream(file);
                                byte[] data = readBytes(inStream);
                                outStream.write(data);
                                file.delete();
                                try {
                                    if (outStream != null)
                                        outStream.close();
                                    if (inStream != null)
                                        inStream.close();
                                } catch (IOException e) {
                                        logger.error(e);
                                }
                            }
                        } catch (Exception e) {
                            logger.error((Object) "Error: ", (Throwable) e);
                        }
                        continue;
                    }
                    logger.info((Object) ("file not match format " + fileFormat));
                }
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
    }

    private void processRsaEncrypt(JsonObject partner) {
        try {
            logger.info("Partner Information:" + partner.toString());
            String folderFrom = partner.getString("folder_from");
            String folderTo = partner.getString("folder_to");
            String folderBackup = partner.getString("folder_backup");
            String publicKeyFile = partner.getString("public_key");
            String backup = partner.getString("backup");
            String fileFormat = partner.getString("file_format");
            String fileExtend = partner.getString("file_extend");
            File folder = new File(folderFrom);
            if (folder.isDirectory()) {
                for (File file : folder.listFiles()) {
                    if (file.isFile()) {
                        logger.info("Process file " + file.getName() + ", file format=" + fileFormat);
                        Pattern pattern = Pattern.compile(fileFormat);
                        Matcher matcher = pattern.matcher(file.getName());
                        if (matcher.find()) {
                            try {

                                String urlOriFile = folderFrom + file.getName();
                                String urlEncFile = folderTo + file.getName() + fileExtend;
                                PublicKey publicKey = PKICrypt.getPublickey(publicKeyFile);
                                RSAAlgorithm.PGPencrypt(urlOriFile, urlEncFile, publicKey);
                                if ("yes".equalsIgnoreCase(backup)) {
                                    File dir = new File(folderBackup);
                                    File fileBackup = new File(dir, file.getName());
                                    fileBackup.deleteOnExit();
                                    boolean success = file.renameTo(new File(dir, file.getName()));
                                    if (!success) {
                                        logger.info("can not move file:" + file.getName());
                                        file.deleteOnExit();
                                    } else {
                                        logger.info("moved file:" + file.getName());
                                    }
                                }
                            } catch (Exception e) {// Catch exception if any
                                logger.error("Error: ", e);
                            }
                        } else {
                            logger.info("file not match format " + fileFormat);
                        }
                    }
                }
            } else {
                logger.info("folderScan: " + folderFrom + " is not a directory");
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
    }

    private void processPgpEncrypt(JsonObject partner) {
        try {
            logger.info("Partner Information:" + partner.toString());
            String folderFrom = partner.getString("folder_from");
            String folderTo = partner.getString("folder_to");
            String folderBackup = partner.getString("folder_backup");
            String publicKeyFile = partner.getString("public_key");
            String backup = partner.getString("backup");
            String fileFormat = partner.getString("file_format");
            String fileExtend = partner.getString("file_extend");
            File folder = new File(folderFrom);
            if (folder.isDirectory()) {
                for (File file : folder.listFiles()) {
                    if (file.isFile()) {
                        logger.info("Process file " + file.getName() + ", file format=" + fileFormat);
                        Pattern pattern = Pattern.compile(fileFormat);
                        Matcher matcher = pattern.matcher(file.getName());
                        if (matcher.find()) {
                            try {
                                String urlOriFile = folderFrom + file.getName();
                                String urlEncFile = folderTo + file.getName() + fileExtend;
                                PGPHandler.encryptPGP(urlOriFile, Task.class.getClassLoader().getResource(publicKeyFile).getPath(), urlEncFile, null);
                                if ("yes".equalsIgnoreCase(backup)) {
                                    File dir = new File(folderBackup);
                                    File fileBackup = new File(dir, file.getName());
                                    fileBackup.delete();
                                    boolean success = file.renameTo(new File(dir, file.getName()));
                                    if (!success) {
                                        logger.info("can not move file:" + file.getName());
                                        file.delete();
                                    } else {
                                        logger.info("moved file:" + file.getName());
                                    }
                                }
                            } catch (Exception e) {// Catch exception if any
                                logger.error("Error: ", e);
                            }
                        } else {
                            logger.info("file not match format " + fileFormat);
                        }
                    }
                }
            } else {
                logger.info("folderScan: " + folderFrom + " is not a directory");
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
    }

    private void processRsaDecrypt(JsonObject partner) {
        try {
            logger.info("Partner Information:" + partner.toString());
            String folderFrom = partner.getString("folder_from");
            String folderTo = partner.getString("folder_to");
            String folderBackup = partner.getString("folder_backup");
            String privateKeyFile = partner.getString("private_key");
            String backup = partner.getString("backup");
            String fileFormat = partner.getString("file_format");
            String fileExtend = partner.getString("file_extend");
            String passphrase = partner.getString("passphrase");
            File folder = new File(folderFrom);
            if (folder.isDirectory()) {
                for (File file : folder.listFiles()) {
                    if (file.isFile()) {
                        logger.info("Process file " + file.getName() + ", file format=" + fileFormat);
                        Pattern pattern = Pattern.compile(fileFormat);
                        Matcher matcher = pattern.matcher(file.getName());
                        if (matcher.find()) {
                            try {
                                String urlOriFile = folderFrom + file.getName();
                                String urlDeFile = folderTo + file.getName().replace(fileExtend, "");
                                PrivateKey privateKey = PKICrypt.getPrivateKey(privateKeyFile, passphrase);
                                if (privateKey == null) {
                                    logger.info("can not load private key");
                                }
                                RSAAlgorithm.PGPdecrypt(urlOriFile, urlDeFile, privateKey);
                                if ("yes".equalsIgnoreCase(backup)) {
                                    File dir = new File(folderBackup);
                                    File fileBackup = new File(dir, file.getName());
                                    fileBackup.deleteOnExit();
                                    boolean success = file.renameTo(new File(dir, file.getName()));
                                    if (!success) {
                                        logger.info("can not move file:" + file.getName());
                                        file.deleteOnExit();
                                    } else {
                                        logger.info("moved file:" + file.getName());
                                    }
                                }
                            } catch (Exception e) {// Catch exception if any
                                logger.error("Error: ", e);
                            }
                        } else {
                            logger.info("file not match format " + fileFormat);
                        }
                    }
                }
            } else {
                logger.info("folderScan: " + folderFrom + " is not a directory");
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
    }

    private void processPgpDecrypt(JsonObject partner) {
        try {
            logger.info("Partner Information:" + partner.toString());
            String folderFrom = partner.getString("folder_from");
            String folderTo = partner.getString("folder_to");
            String folderBackup = partner.getString("folder_backup");
            String privateKeyFile = partner.getString("private_key");
            String backup = partner.getString("backup");
            String fileFormat = partner.getString("file_format");
            String fileExtend = partner.getString("file_extend");
            String passphrase = partner.getString("passphrase");
            File folder = new File(folderFrom);
            if (folder.isDirectory()) {
                for (File file : folder.listFiles()) {
                    if (file.isFile()) {
                        logger.info("Process file " + file.getName() + ", file format=" + fileFormat);
                        Pattern pattern = Pattern.compile(fileFormat);
                        Matcher matcher = pattern.matcher(file.getName());
                        if (matcher.find()) {
                            try {
                                String urlOriFile = folderFrom + file.getName();
                                String urlDeFile = folderTo + file.getName().replace(fileExtend, "");
                                PGPHandler.decryptPGP(urlOriFile, Task.class.getClassLoader().getResource(privateKeyFile).getPath(), urlDeFile, passphrase);
                                if ("yes".equalsIgnoreCase(backup)) {
                                    File dir = new File(folderBackup);
                                    File fileBackup = new File(dir, file.getName());
                                    fileBackup.delete();
                                    boolean success = file.renameTo(new File(dir, file.getName()));
                                    if (!success) {
                                        logger.info("can not move file:" + file.getName());
                                        file.delete();
                                    } else {
                                        logger.info("moved file:" + file.getName());
                                    }
                                }
                            } catch (Exception e) {// Catch exception if any
                                logger.error("Error: ", e);
                            }
                        } else {
                            logger.info("file not match format " + fileFormat);
                        }
                    }
                }
            } else {
                logger.info("folderScan: " + folderFrom + " is not a directory");
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
    }

    private void processOutSftp(JsonObject partner, boolean isCreateTmp) {

        try {
            logger.info((Object) ("Partner Information:" + partner.toString()));
            String folderFrom = partner.getString("folder_from");
            String folderTo = partner.getString("folder_to");
            String folderBackup = partner.getString("folder_backup");
            String host = partner.getString("host");
            int port = partner.getInteger("port");
            String userName = partner.getString("user_name");
            String password = partner.getString("password");
            JsonArray privateKey = partner.getJsonArray("private_key");
            String key = "";
            if (privateKey != null && privateKey.getList().size() > 0) {
                for (int j = 0; j < privateKey.getList().size(); ++j) {
                    key = key + privateKey.getString(j);
                    if (j >= privateKey.getList().size() - 1)
                        continue;
                    key = key + "\n";
                }
            }
            String fileFormat = partner.getString("file_format");
            String backup = partner.getString("backup");

            File folder = new File(folderFrom);
            if (folder.isDirectory()) {
                for (File file : folder.listFiles()) {
                    if (!file.isFile())
                        continue;
                    logger.info((Object) ("Process file " + file.getName() + ", file format=" + fileFormat));
                    Pattern pattern = Pattern.compile(fileFormat);
                    Matcher matcher = pattern.matcher(file.getName());
                    if (matcher.find()) {
                        try {
                            InputStream fileStream = new FileInputStream(file);
                            byte[] data = readBytes(fileStream);
                            Sftp.pushSftp(host, port, userName, password, key, folderTo, file.getName(), data, isCreateTmp);
                            if ("yes".equalsIgnoreCase(backup)) {
                                File dir = new File(folderBackup);
                                File fileBackup = new File(dir, file.getName());
                                FileOutputStream outStream = new FileOutputStream(fileBackup);
                                ((OutputStream) outStream).write(data);
                                try {
                                    if (outStream != null) {
                                        ((OutputStream) outStream).close();
                                    }
                                } catch (IOException e) {
                                    logger.error((Object) e);
                                }
                                file.delete();
                            }
                            try {
                                if (fileStream == null)
                                    continue;
                                fileStream.close();
                            } catch (IOException e) {
                                logger.error((Object) e);
                            }
                        } catch (Exception e) {
                            logger.error((Object) "Error: ", (Throwable) e);
                        }
                        continue;
                    }
                    logger.info((Object) ("file not match format " + fileFormat));
                }
            }
            logger.info((Object) ("folderScan: " + folderFrom + " is not a directory"));
        } catch (Exception ex) {
            logger.error((Object) ex);
        }
    }

    private void processOutFtp(JsonObject partner) {
        try {
            logger.info("Partner Information:" + partner.toString());
            String folderFrom = partner.getString("folder_from");
            String folderTo = partner.getString("folder_to");
            String folderBackup = partner.getString("folder_backup");
            String host = partner.getString("host");
            int port = partner.getInteger("port");
            String userName = partner.getString("user_name");
            String password = partner.getString("password");
            String fileFormat = partner.getString("file_format");
            String backup = partner.getString("backup");
            if ("yes".equalsIgnoreCase(backup)) {
                Ftp.pushFtp(host, port, userName, password, folderFrom, folderTo, folderBackup);
            } else {
                Ftp.pushFtp(host, port, userName, password, folderFrom, folderTo, "");
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
    }

    private void processInFtp(JsonObject partner) {
        try {
            logger.info("Partner Information:" + partner.toString());
            String folderFrom = partner.getString("folder_from");
            String folderTo = partner.getString("folder_to");
            String folderBackup = partner.getString("folder_backup");
            String folderCheckExist = partner.getString("folder_check_exist");
            String host = partner.getString("host");
            int port = partner.getInteger("port");
            String userName = partner.getString("user_name");
            String password = partner.getString("password");
            String backup = partner.getString("backup");
            if ("yes".equalsIgnoreCase(backup)) {
                Ftp.getFtp(host, port, userName, password, folderFrom, folderTo, folderBackup, folderCheckExist);
            } else {
                Ftp.getFtp(host, port, userName, password, folderFrom, folderTo, null, folderCheckExist);
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
    }

    private void processInSftp(JsonObject partner) {
        try {
            logger.info((Object) ("Partner Information:" + partner.toString()));
            String folderFrom = partner.getString("folder_from");
            String folderTo = partner.getString("folder_to");
            String folderBackup = partner.getString("folder_backup");
            String host = partner.getString("host");
            int port = partner.getInteger("port");
            String userName = partner.getString("user_name");
            String password = partner.getString("password");
            JsonArray privateKey = partner.getJsonArray("private_key");
            String key = "";
            if (privateKey != null && privateKey.getList().size() > 0) {
                for (int j = 0; j < privateKey.getList().size(); ++j) {
                    key = key + privateKey.getString(j);
                    if (j >= privateKey.getList().size() - 1)
                        continue;
                    key = key + "\n";
                }
            }
            String fileFormat = partner.getString("file_format");
            String backup = partner.getString("backup");
            String skipFolder = partner.getString("skip_folder");
            if ("yes".equalsIgnoreCase(backup)) {

                Sftp.getSftp(host, port, userName, password, key, folderFrom, folderTo, folderBackup, fileFormat, skipFolder);

            } else {

                Sftp.getSftp(host, port, userName, password, key, folderFrom, folderTo, null, fileFormat, skipFolder);

            }
        } catch (Exception ex) {
            logger.error((Object) ex);
        }
    }

    private void processLocal(JsonObject partner) {
        try {
            logger.info("Partner Information:" + partner.toString());
            String folderScan = partner.getString("folder_from");
            String folderTo = partner.getString("folder_to");
            String fileFormat = partner.getString("file_format");
            LocalFile.moveFileLocal(folderScan, fileFormat, folderTo);
        } catch (Exception ex) {
            logger.error(ex);
        }
    }

    private void processAesDecrypt(JsonObject partner) {
        try {
            logger.info("Partner Information:" + partner.toString());
            String folderFrom = partner.getString("folder_from");
            String folderTo = partner.getString("folder_to");
            String folderBackup = partner.getString("folder_backup");
            String keyStr = partner.getString("key");
            String backup = partner.getString("backup");
            String fileFormat = partner.getString("file_format");
            String fileExtend = partner.getString("file_extend");
            String targetExtend = partner.getString("target_extend");
            String initVector = partner.getString("init_vector");

            File folder = new File(folderFrom);
            if (folder.isDirectory()) {
                for (File file : folder.listFiles()) {
                    if (file.isFile()) {
                        logger.info("Process file " + file.getName() + ", file format=" + fileFormat);
                        Pattern pattern = Pattern.compile(fileFormat);
                        Matcher matcher = pattern.matcher(file.getName());
                        if (matcher.find()) {
                            InputStream in = null;
                            OutputStream out = null;
                            try {
                                String urlOriFile = folderFrom + file.getName();
                                String urlDeFile = folderTo + file.getName().replace(fileExtend, targetExtend);

                                in = new FileInputStream(new File(urlOriFile));
                                byte[] data = readBytes(in);
                                byte[] dataDecoded = AESUtil.decrypt(keyStr, initVector, new String(data)).getBytes();
                                out = new FileOutputStream(new File(urlDeFile));
                                out.write(dataDecoded);

                                if ("yes".equalsIgnoreCase(backup)) {
                                    File dir = new File(folderBackup);
                                    File fileBackup = new File(dir, file.getName());
                                    fileBackup.delete();
                                    boolean success = file.renameTo(new File(dir, file.getName()));
                                    if (!success) {
                                        logger.info("processAesDecrypt can not move file:" + file.getName());
                                        file.delete();
                                    } else {
                                        logger.info("processAesDecrypt moved file:" + file.getName());
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("Error: ", e);
                            } finally {
                                if (out != null) {
                                    out.close();
                                }
                                if (in != null) {
                                    in.close();
                                }
                            }
                        } else {
                            logger.info("processAesDecrypt file not match format " + fileFormat);
                        }
                    }
                }
            } else {
                logger.info("processAesDecrypt folderScan: " + folderFrom + " is not a directory");
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
    }

    public static byte[] readBytes(InputStream inputStream) throws IOException {
        byte[] b = new byte[1024];
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        int c;
        while ((c = inputStream.read(b)) != -1) {
            os.write(b, 0, c);
        }
        return os.toByteArray();
    }

}

package vn.onepay.transfer.rsa;

import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Arrays;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * <AUTHOR>
 */
public class RSAAlgorithm {
    private static Log logger = LogFactory.getLog(RSAAlgorithm.class.getName());
    // java.util.Base64.Encoder base64en = java.util.Base64.getEncoder();
    private static java.util.Base64.Decoder base64de = java.util.Base64.getDecoder();
    // Các ký tự xuống dòng \r \n
    private static char eol1 = (char) 13;
    private static char eol2 = (char) 10;
    public static String SYM_ALGORITHM = "AES";
    public static int SESSION_KEY_LENGTH = 128;

    /**
     * @param args the command line arguments
     */
    public static void main(String[] args) throws Exception {
        RSAAlgorithm algorithm = new RSAAlgorithm();
        algorithm.encryptFile();
    }

    public void encryptFile() throws Exception {
        String oriFile = "/home/<USER>/projects/onepay/cdr-transfer/src/main/conf/napas/test.dat";
        String encFile = "/home/<USER>/projects/onepay/cdr-transfer/src/main/conf/napas/test.dat.pgp";
        PublicKey publicKey = PKICrypt
                .getPublickey("/home/<USER>/projects/onepay/cdr-transfer/src/main/conf/napas/onepay.cer");
        PGPencrypt(oriFile, encFile, publicKey);

    }

    public void decryptFile() throws Exception {
        String encFile = "/home/<USER>/projects/onepay/cdr-transfer/src/main/conf/napas/052120_ACQ_ONEPAYWL3_971018_1_TC_ECOM.dat.pgp";
        String outFile = "/home/<USER>/projects/onepay/cdr-transfer/src/main/conf/napas/052120_ACQ_ONEPAYWL3_971018_1_TC_ECOM.dat";
        PrivateKey privateKey = PKICrypt.getPrivateKey(
                "/home/<USER>/projects/onepay/cdr-transfer/src/main/conf/napas/onepay_pri.pem", "changeit");
        PGPdecrypt(encFile, outFile, privateKey);
    }

    public static void PGPencrypt(String originalFile, String encryptedFile, PublicKey publicKey) throws Exception {
        logger.info(new Date().toString() + ":  ----Begin encrypt----");
        // Sinh khóa phiên ngẫu nhiên sử dụng thuật toán AES-128-EBC
        SecretKey sessionKey = RSAAlgorithm.generateSessionkey();

        Path path = Paths.get(originalFile);
        byte[] data = Files.readAllBytes(path);
        logger.info("Read file successfully:" + data.length + " bytes");
        // Mã hóa dữ liệu sử dụng khóa phiên đối xứng AES
        byte[] encData = symmetricEncrypt(data, sessionKey);
        logger.info("Encrypt data successfully");
        // Mã hóa khóa phiên sử dụng thuật toán mã hóa bất đối xứng RSA với public key.
        byte[] sessionKeyByte = sessionKey.getEncoded();
        byte[] encSessionKey = PKICrypt.encrypt(sessionKeyByte, publicKey);
        logger.info("sessionKeyByte:" + sessionKeyByte.length);
        logger.info("encSessionKey:" + encSessionKey.length);

        // Encode base64 khóa phiên và dữ liệu sau khi được mã hóa
        String base64EncData = Base64.encode(encData);
        String base64EncSessionKey = Base64.encode(encSessionKey).replaceAll("(?:\\r\\n|\\n\\r|\\n|\\r)", "");
        logger.info(new Date().toString() + ": Encode base64 successfully");
        // Ghi ra file, khóa phiên và dữ liệu sau khi được mã hóa nằm trên 2 dòng
        BufferedWriter bw = new BufferedWriter(new FileWriter(encryptedFile));
        bw.write(base64EncSessionKey);
        // chèn thêm ký tự xuống dòng
        bw.write(eol1);
        bw.write(eol2);
        bw.write(base64EncData);
        bw.flush();
        bw.close();
        logger.info(new Date().toString() + ": Write encrypted file successfully");
    }

    public static void PGPdecrypt(String encryptedFile, String decryptedFile, PrivateKey privateKey) throws Exception {
        logger.info(new Date().toString() + ":  ----Begin decrypt----");
        Path path = Paths.get(encryptedFile);
        byte[] allContent = Files.readAllBytes(path);
        logger.info(new Date().toString() + ": Read file successfully");

        // Loại bỏ các ký tự xuống dòng vô nghĩa ở đầu file
        int i = 0, s = 0;
        while (((char) allContent[i] == eol1) || ((char) allContent[i] == eol2))
            i++;
        s = i;
        // Tìm đến ký tự xuống dòng để cắt chuỗi
        while ((eol1 != (char) allContent[i]) && (eol2 != (char) allContent[i]))
            i++;
        // Cắt lấy phần khóa phiên được mã hóa và encode
        byte[] base64EncSessionKey = Arrays.copyOfRange(allContent, s, i);
        // Loại bỏ các ký tự xuống dòng vô nghĩa ở giữa file
        while (((char) allContent[i] == eol1) || ((char) allContent[i] == eol2))
            i++;
        int len = allContent.length;
        // Loại bỏ các ký tự xuống dòng vô nghĩa ở cuối file
        while (((char) allContent[len - 1] == eol1) || ((char) allContent[len - 1] == eol2))
            len--;
        // Cắt lấy phần dữ liệu đã mã hóa và encode
        byte[] base64EncData = Arrays.copyOfRange(allContent, i, len);

        // Decode base64 khóa và dữ liệu
        byte[] encSessionKey = base64de.decode(base64EncSessionKey);
        byte[] decData = base64de.decode(base64EncData);
        logger.info(new Date().toString() + ": Decode base64 successfully");
        // Giải mã khóa phiên sử dụng private key
        logger.info("end session key:" + encSessionKey.length);
        byte[] sessionKeyByte = PKICrypt.decrypt(encSessionKey, privateKey);
        logger.info("length:" + sessionKeyByte.length);
        SecretKey sessionKey = new SecretKeySpec(sessionKeyByte, RSAAlgorithm.SYM_ALGORITHM);
        logger.info(new Date().toString() + ": Decrypt session key successfully");
        // Giải mã dữ liệu sử dụng khóa phiên lấy được trong bước trước
        byte[] data = symmetricDecrypt(decData, sessionKey);
        logger.info(new Date().toString() + ": Decrypt date successfully");
        // Ghi file
        path = Paths.get(decryptedFile);
        Files.write(path, data);
        logger.info(new Date().toString() + ": Write data file successfully");

    }

    private static SecretKey generateSessionkey() {
        KeyGenerator keyGen;
        try {
            keyGen = KeyGenerator.getInstance(SYM_ALGORITHM);
            keyGen.init(SESSION_KEY_LENGTH);
            return keyGen.generateKey();
        } catch (NoSuchAlgorithmException ex) {
            Logger.getLogger(RSAAlgorithm.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    private static byte[] symmetricEncrypt(byte[] messageB, SecretKey key) throws Exception {
        // SecretKey key = new SecretKeySpec(keyBytes, "DESede");
        Cipher cipher = Cipher.getInstance(SYM_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] buf = cipher.doFinal(messageB);
        return buf;
    }

    private static byte[] symmetricDecrypt(byte[] encryptedTextB, SecretKey key) throws Exception {

        Cipher decipher = Cipher.getInstance(SYM_ALGORITHM);
        decipher.init(Cipher.DECRYPT_MODE, key);

        byte[] plainText = decipher.doFinal(encryptedTextB);
        return plainText;
    }

}
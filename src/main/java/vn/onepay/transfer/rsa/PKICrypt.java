package vn.onepay.transfer.rsa;

import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.openssl.PEMDecryptorProvider;
import org.bouncycastle.openssl.PEMEncryptedKeyPair;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.bouncycastle.openssl.jcajce.JcePEMDecryptorProviderBuilder;

import javax.crypto.Cipher;
import java.io.*;
import java.security.*;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.X509EncodedKeySpec;

// import org.bouncycastle.openssl.PEMReader;
// import org.bouncycastle.openssl.PasswordFinder;

/**
 * <AUTHOR>
 */
public class PKICrypt {
    private static Log logger = LogFactory.getLog(RSAAlgorithm.class.getName());
    /**
     * String to hold name of the encryption algorithm.
     */
    public static String ALGORITHM = "RSA";
    public static int KEY_PAIR_LENGTH = 2048;
    public static String PRIVATE_KEY_FILE = "/opt/transfer/classes/keys/onepay_pri.pem";
    public static String PUBLIC_KEY_FILE = "/opt/transfer/classes/keys/onepay.cer";
    public static String PUBLIC_KEY_FILE_2 = "/opt/transfer/classes/keys/onepay.cer";

    /**
     * Generate key which contains a pair of private and public key using 1024
     * bytes. Store the set of keys in Prvate.key and Public.key files.
     *
     * @throws NoSuchAlgorithmException
     * @throws IOException
     * @throws FileNotFoundException
     */
    public static void generateKey() {
        try {
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance(ALGORITHM);
            keyGen.initialize(KEY_PAIR_LENGTH);
            KeyPair key = keyGen.generateKeyPair();
            PublicKey publicKey = key.getPublic();
            PrivateKey privateKey = key.getPrivate();

            File privateKeyFile = new File(PRIVATE_KEY_FILE);
            File publicKeyFile = new File(PUBLIC_KEY_FILE);

            // Create files to store public and private key
            if (privateKeyFile.getParentFile() != null) {
                privateKeyFile.getParentFile().mkdirs();
            }
            privateKeyFile.createNewFile();

            if (publicKeyFile.getParentFile() != null) {
                publicKeyFile.getParentFile().mkdirs();
            }
            publicKeyFile.createNewFile();

            // Saving the Public key in a file
            ObjectOutputStream publicKeyOS = new ObjectOutputStream(
                    new FileOutputStream(publicKeyFile));
            publicKeyOS.writeObject(key.getPublic());
            publicKeyOS.close();

            // Saving the Private key in a file
            ObjectOutputStream privateKeyOS = new ObjectOutputStream(
                    new FileOutputStream(privateKeyFile));
            privateKeyOS.writeObject(key.getPrivate());
            privateKeyOS.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * The method checks if the pair of public and private key has been
     * generated.
     *
     * @return flag indicating if the pair of keys were generated.
     */
    public static boolean areKeysPresent() {

        File privateKey = new File(PRIVATE_KEY_FILE);
        File publicKey = new File(PUBLIC_KEY_FILE);

        if (privateKey.exists() && publicKey.exists()) {
            return true;
        }
        return false;
    }

    /**
     * Encrypt the plain text using public key.
     *
     * @param text : original plain text
     * @param key  :The public key
     * @return Encrypted text
     * @throws Exception
     */
    public static byte[] encrypt(byte[] data, PublicKey publicKey) {
        byte[] cipherText = null;
        try {
            // get an RSA cipher object and print the provider
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            // encrypt the plain text using the public key     
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            cipherText = cipher.doFinal(data);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cipherText;
    }

    /**
     * Decrypt text using private key.
     *
     * @param text :encrypted text
     * @param key  :The private key
     * @return plain text
     * @throws Exception
     */
    public static byte[] decrypt(byte[] encData, PrivateKey privateKey) {
        byte[] dectyptedTextB = null;
        try {
            // get an RSA cipher object and print the provider
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            // decrypt the text using the private key
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            dectyptedTextB = cipher.doFinal(encData);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return dectyptedTextB;
    }

    public static PublicKey getPublickey(String sPublicKeyName) {
        PublicKey pk = null;
        try {
            String sExtFile = getExtension(sPublicKeyName);
            if (sExtFile.equals("pem")) {
                File f = new File(sPublicKeyName);
                FileInputStream fis = new FileInputStream(f);
                DataInputStream dis = new DataInputStream(fis);
                byte[] keyBytes = new byte[(int) f.length()];
                dis.readFully(keyBytes);
                dis.close();
                String temp = new String(keyBytes);
                String publicKeyPEM = temp.replace("-----BEGIN PUBLIC KEY-----\n", "");
                publicKeyPEM = publicKeyPEM.replace("-----END PUBLIC KEY-----", "");
                Base64 b64 = new Base64();
                byte[] decoded = b64.decode(publicKeyPEM);

                X509EncodedKeySpec spec
                        = new X509EncodedKeySpec(decoded);
                KeyFactory kf = KeyFactory.getInstance("RSA");
                pk = kf.generatePublic(spec);
            } else if (sExtFile.equals("cer")) {
                FileInputStream fin = new FileInputStream(sPublicKeyName);
                CertificateFactory f = CertificateFactory.getInstance("X.509");
                X509Certificate certificate = (X509Certificate) f.generateCertificate(fin);
                pk = certificate.getPublicKey();
            } else if (sExtFile.equals("key")) {
                //PKICrypt.PUBLIC_KEY_FILE
                ObjectInputStream inputStream = new ObjectInputStream(new FileInputStream(PKICrypt.class.getClassLoader().getResource("_.onepay.vn-2020.key").getPath()));
                pk = (PublicKey) inputStream.readObject();
            }
            return pk;
        } catch (Exception ex) {
            return null;
        }
    }

    public static PrivateKey getPrivateKey(String sPrivateKeyName, String password) {
        PrivateKey pk = null;
        try {
            String sExtFile = getExtension(sPrivateKeyName);
            if (sExtFile.equals("pem")) {
                PEMParser pemParser = new PEMParser(new FileReader(sPrivateKeyName));
                Object keypair = pemParser.readObject();
                PrivateKeyInfo keyInfo;
                if (keypair instanceof PEMEncryptedKeyPair) {
                    PEMDecryptorProvider decyptor = new JcePEMDecryptorProviderBuilder().build(password.toCharArray());
                    PEMKeyPair decryptKeyPair = ((PEMEncryptedKeyPair) keypair).decryptKeyPair(decyptor);
                    keyInfo = decryptKeyPair.getPrivateKeyInfo();
                } else {
                    keyInfo = ((PEMKeyPair) keypair).getPrivateKeyInfo();
                }
                pk = new JcaPEMKeyConverter().getPrivateKey(keyInfo);
            } else if (sExtFile.equals("key")) {
                ObjectInputStream inputStream = new ObjectInputStream(new FileInputStream(PKICrypt.PRIVATE_KEY_FILE));
                pk = (PrivateKey) inputStream.readObject();
            }
            return pk;
        } catch (Exception ex) {
            logger.error("getPrivateKey error " + sPrivateKeyName, ex);
            return null;
        }
    }

    private static String getExtension(String sFile) {
        String extension = "";
        int i = sFile.lastIndexOf('.');
        int p = Math.max(sFile.lastIndexOf('/'), sFile.lastIndexOf('\\'));

        if (i > p) {
            extension = sFile.substring(i + 1);
        }
        return extension;

    }

}
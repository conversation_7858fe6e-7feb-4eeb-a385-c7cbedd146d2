[Unit]
Description=ssp
Documentation=http://ssp.onepay.vn/en/docs/
After=network.target remote-fs.target nss-lookup.target

[Service]
ExecStart=/usr/java/jdk1.8.0_92/bin/java \
 -XX:+UseG1GC -Xms20M -Xmx100M \
 -Djava.util.logging.manager=org.apache.logging.log4j.jul.LogManager \
 -Dlog=/var/log/ssp/ssp.log \
 -cp /opt/ssp/lib/* \
 vn.onepay.ssp.Main

WorkingDirectory=/opt/ssp

User=ssp
Group=ssp
LimitNOFILE=20000

[Install]
WantedBy=multi-user.target
#Product

rsync -av --del --force -e 'ssh -p 7290' --log-file="/root/projects/onepay-paygate/transfer/src/main/service/rsync-$(date +%Y%m%d).log"   /root/projects/onepay-paygate/transfer/target/classes/vn/  root@r:/opt/transfer/classes/vn/
rsync -av --del --force -e 'ssh -p 7290' --log-file="/root/projects/onepay-paygate/transfer/src/main/service/rsync-$(date +%Y%m%d).log"   /root/projects/onepay-paygate/transfer/target/classes/  root@r:/opt/transfer/classes/
rsync -av --del --force -e 'ssh -p 7290' --log-file="/root/projects/onepay-paygate/transfer/src/main/service/rsync-$(date +%Y%m%d).log"   /Applications/DATA/projects/codes/reconciliation/out/artifacts/transfer/lib/  root@r:/opt/transfer/lib/

// import org.apache.commons.codec.binary.Base64;

// import javax.crypto.Cipher;
// import java.io.*;
// import java.nio.ByteBuffer;
// import java.security.*;
// import java.security.interfaces.RSAPrivateKey;
// import java.security.interfaces.RSAPublicKey;
// import java.security.spec.EncodedKeySpec;
// import java.security.spec.InvalidKeySpecException;
// import java.security.spec.PKCS8EncodedKeySpec;
// import java.security.spec.X509EncodedKeySpec;

// import org.bouncycastle.jce.provider.BouncyCastleProvider;
// import org.bouncycastle.openpgp.PGPPrivateKey;
// import org.bouncycastle.openpgp.PGPPublicKey;
// import org.bouncycastle.openpgp.PGPSecretKey;
// import org.bouncycastle.util.io.pem.PemObject;
// import org.bouncycastle.util.io.pem.PemReader;
// import vn.onepay.transfer.job.PemFile;

// import static com.sun.xml.internal.ws.spi.db.BindingContextFactory.LOGGER;

// import java.io.FileNotFoundException;
// import java.io.IOException;
// import java.security.KeyFactory;
// import java.security.NoSuchAlgorithmException;
// import java.security.NoSuchProviderException;
// import java.security.PrivateKey;
// import java.security.PublicKey;
// import java.security.Security;
// import java.security.spec.InvalidKeySpecException;
// import java.security.spec.PKCS8EncodedKeySpec;
// import java.security.spec.X509EncodedKeySpec;


public class PGPExample {
    // public static void main(String[] arg) {
    //     try {
    //         // testEncryptPGP();
    //         testDecryptPGP();
    //     } catch (Exception e) {
    //         e.printStackTrace();
    //     }
    // }

    // private static void testEncryptPGP() {
    //     try {
    //         Security.addProvider(new BouncyCastleProvider());
    //         boolean armor = true;
    //         boolean integrityCheck = true;
    //         KeyBasedLargeFileProcessor.encryptFile("/root/projects/onepay-paygate/transfer/src/test/conf/encrypted.pgp", "/root/projects/onepay-paygate/transfer/src/test/conf/test_raw.txt", "/root/projects/onepay-paygate/transfer/src/test/conf/pub.asc", armor, integrityCheck);
    //     } catch (Exception ex) {
    //         ex.printStackTrace();
    //     }
    // }
    // private static void testDecryptPGP() {
    //     try {
    //         Security.addProvider(new BouncyCastleProvider());
    //         KeyBasedLargeFileProcessor.decryptFile("/root/projects/onepay-paygate/transfer/src/test/conf/encrypted.pgp", "/root/projects/onepay-paygate/transfer/src/test/conf/pair.asc", "4kva093N3".toCharArray(),"/root/projects/onepay-paygate/transfer/src/test/out/");
    //     } catch (Exception ex) {
    //         ex.printStackTrace();
    //     }
    // }

}
